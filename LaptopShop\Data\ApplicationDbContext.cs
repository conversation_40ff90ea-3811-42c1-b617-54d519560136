using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using LaptopShop.Models;

namespace LaptopShop.Data
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        public DbSet<Category> Categories { get; set; }
        public DbSet<Brand> Brands { get; set; }
        public DbSet<Laptop> Laptops { get; set; }
        public DbSet<Order> Orders { get; set; }
        public DbSet<OrderDetail> OrderDetails { get; set; }
        public DbSet<Review> Reviews { get; set; }
        public DbSet<CartItem> CartItems { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure relationships
            modelBuilder.Entity<Laptop>()
                .HasOne(l => l.Category)
                .WithMany(c => c.Laptops)
                .HasForeignKey(l => l.CategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Laptop>()
                .HasOne(l => l.Brand)
                .WithMany(b => b.Laptops)
                .HasForeignKey(l => l.BrandId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Order>()
                .HasOne(o => o.User)
                .WithMany(u => u.Orders)
                .HasForeignKey(o => o.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<OrderDetail>()
                .HasOne(od => od.Order)
                .WithMany(o => o.OrderDetails)
                .HasForeignKey(od => od.OrderId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<OrderDetail>()
                .HasOne(od => od.Laptop)
                .WithMany(l => l.OrderDetails)
                .HasForeignKey(od => od.LaptopId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<CartItem>()
                .HasOne(ci => ci.User)
                .WithMany(u => u.CartItems)
                .HasForeignKey(ci => ci.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<CartItem>()
                .HasOne(ci => ci.Laptop)
                .WithMany(l => l.CartItems)
                .HasForeignKey(ci => ci.LaptopId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Review>()
                .HasOne(r => r.User)
                .WithMany(u => u.Reviews)
                .HasForeignKey(r => r.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Review>()
                .HasOne(r => r.Laptop)
                .WithMany(l => l.Reviews)
                .HasForeignKey(r => r.LaptopId)
                .OnDelete(DeleteBehavior.Cascade);

            // Configure indexes
            modelBuilder.Entity<Laptop>()
                .HasIndex(l => l.Name);

            modelBuilder.Entity<Order>()
                .HasIndex(o => o.OrderNumber)
                .IsUnique();

            // Seed data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            var seedDate = new DateTime(2024, 1, 1);

            // Seed Categories
            modelBuilder.Entity<Category>().HasData(
                new Category { Id = 1, Name = "Laptop Gaming", Description = "Laptop chuyên dụng cho game thủ", CreatedAt = seedDate },
                new Category { Id = 2, Name = "Laptop Văn Phòng", Description = "Laptop phù hợp cho công việc văn phòng", CreatedAt = seedDate },
                new Category { Id = 3, Name = "Laptop Đồ Họa", Description = "Laptop chuyên dụng cho thiết kế đồ họa", CreatedAt = seedDate },
                new Category { Id = 4, Name = "Laptop Mỏng Nhẹ", Description = "Laptop siêu mỏng nhẹ, dễ di chuyển", CreatedAt = seedDate },
                new Category { Id = 5, Name = "Laptop Workstation", Description = "Laptop hiệu năng cao cho chuyên gia", CreatedAt = seedDate }
            );

            // Seed Brands
            modelBuilder.Entity<Brand>().HasData(
                new Brand { Id = 1, Name = "ASUS", Description = "Thương hiệu laptop hàng đầu từ Đài Loan", CreatedAt = seedDate },
                new Brand { Id = 2, Name = "Dell", Description = "Thương hiệu laptop nổi tiếng từ Mỹ", CreatedAt = seedDate },
                new Brand { Id = 3, Name = "HP", Description = "Thương hiệu laptop uy tín từ Mỹ", CreatedAt = seedDate },
                new Brand { Id = 4, Name = "Lenovo", Description = "Thương hiệu laptop từ Trung Quốc", CreatedAt = seedDate },
                new Brand { Id = 5, Name = "Acer", Description = "Thương hiệu laptop từ Đài Loan", CreatedAt = seedDate },
                new Brand { Id = 6, Name = "MSI", Description = "Thương hiệu laptop gaming nổi tiếng", CreatedAt = seedDate },
                new Brand { Id = 7, Name = "Apple", Description = "Thương hiệu MacBook từ Mỹ", CreatedAt = seedDate }
            );

            // Seed Laptops
            modelBuilder.Entity<Laptop>().HasData(
                new Laptop
                {
                    Id = 1, Name = "ASUS ROG Strix G15", Model = "G513QM",
                    Description = "Laptop gaming mạnh mẽ với AMD Ryzen 7 và RTX 3060",
                    Price = 25990000, SalePrice = 23990000, StockQuantity = 15,
                    MainImageUrl = "https://images.unsplash.com/photo-1603302576837-37561b2e2302?w=500&h=400&fit=crop",
                    Processor = "AMD Ryzen 7 5800H", RAM = "16GB DDR4", Storage = "512GB SSD NVMe",
                    GraphicsCard = "NVIDIA RTX 3060 6GB", ScreenSize = "15.6 inch", Resolution = "1920x1080 144Hz",
                    OperatingSystem = "Windows 11", Weight = "2.3kg", BatteryLife = "6-8 giờ",
                    Connectivity = "WiFi 6, Bluetooth 5.1, USB-C, HDMI", Color = "Đen",
                    IsFeatured = true, CategoryId = 1, BrandId = 1, CreatedAt = seedDate
                },
                new Laptop
                {
                    Id = 2, Name = "Dell XPS 13", Model = "9320",
                    Description = "Laptop mỏng nhẹ cao cấp cho doanh nhân",
                    Price = 32990000, SalePrice = 29990000, StockQuantity = 10,
                    MainImageUrl = "https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=500&h=400&fit=crop",
                    Processor = "Intel Core i7-1260P", RAM = "16GB LPDDR5", Storage = "512GB SSD NVMe",
                    GraphicsCard = "Intel Iris Xe", ScreenSize = "13.4 inch", Resolution = "1920x1200",
                    OperatingSystem = "Windows 11", Weight = "1.27kg", BatteryLife = "10-12 giờ",
                    Connectivity = "WiFi 6E, Bluetooth 5.2, USB-C Thunderbolt 4", Color = "Bạc",
                    IsFeatured = true, CategoryId = 4, BrandId = 2, CreatedAt = seedDate
                },
                new Laptop
                {
                    Id = 3, Name = "HP Pavilion Gaming 15", Model = "15-dk2000",
                    Description = "Laptop gaming giá rẻ hiệu năng tốt",
                    Price = 18990000, SalePrice = 16990000, StockQuantity = 20,
                    MainImageUrl = "https://images.unsplash.com/photo-1525547719571-a2d4ac8945e2?w=500&h=400&fit=crop",
                    Processor = "Intel Core i5-11300H", RAM = "8GB DDR4", Storage = "512GB SSD",
                    GraphicsCard = "NVIDIA GTX 1650 4GB", ScreenSize = "15.6 inch", Resolution = "1920x1080",
                    OperatingSystem = "Windows 11", Weight = "2.23kg", BatteryLife = "5-7 giờ",
                    Connectivity = "WiFi 5, Bluetooth 5.0, USB-A, HDMI", Color = "Xanh đen",
                    IsFeatured = false, CategoryId = 1, BrandId = 3, CreatedAt = seedDate
                },
                new Laptop
                {
                    Id = 4, Name = "Lenovo ThinkPad E15", Model = "Gen 4",
                    Description = "Laptop văn phòng bền bỉ cho doanh nghiệp",
                    Price = 21990000, StockQuantity = 12,
                    MainImageUrl = "https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=500&h=400&fit=crop",
                    Processor = "Intel Core i5-1235U", RAM = "8GB DDR4", Storage = "256GB SSD",
                    GraphicsCard = "Intel Iris Xe", ScreenSize = "15.6 inch", Resolution = "1920x1080",
                    OperatingSystem = "Windows 11 Pro", Weight = "1.7kg", BatteryLife = "8-10 giờ",
                    Connectivity = "WiFi 6, Bluetooth 5.1, USB-A, USB-C, HDMI", Color = "Đen",
                    IsFeatured = false, CategoryId = 2, BrandId = 4, CreatedAt = seedDate
                },
                new Laptop
                {
                    Id = 5, Name = "Acer Aspire 5", Model = "A515-57",
                    Description = "Laptop học tập và làm việc giá tốt",
                    Price = 14990000, SalePrice = 13990000, StockQuantity = 25,
                    MainImageUrl = "https://images.unsplash.com/photo-1484788984921-03950022c9ef?w=500&h=400&fit=crop",
                    Processor = "Intel Core i5-1235U", RAM = "8GB DDR4", Storage = "512GB SSD",
                    GraphicsCard = "Intel Iris Xe", ScreenSize = "15.6 inch", Resolution = "1920x1080",
                    OperatingSystem = "Windows 11", Weight = "1.7kg", BatteryLife = "7-9 giờ",
                    Connectivity = "WiFi 6, Bluetooth 5.1, USB-A, USB-C, HDMI", Color = "Bạc",
                    IsFeatured = true, CategoryId = 2, BrandId = 5, CreatedAt = seedDate
                },
                new Laptop
                {
                    Id = 6, Name = "MSI Gaming GF63", Model = "Thin 11UC",
                    Description = "Laptop gaming mỏng nhẹ với GTX 1650",
                    Price = 19990000, SalePrice = 17990000, StockQuantity = 18,
                    MainImageUrl = "https://images.unsplash.com/photo-1593642702821-c8da6771f0c6?w=500&h=400&fit=crop",
                    Processor = "Intel Core i5-11400H", RAM = "8GB DDR4", Storage = "512GB SSD",
                    GraphicsCard = "NVIDIA GTX 1650 4GB", ScreenSize = "15.6 inch", Resolution = "1920x1080",
                    OperatingSystem = "Windows 11", Weight = "1.86kg", BatteryLife = "5-7 giờ",
                    Connectivity = "WiFi 6, Bluetooth 5.1, USB-A, USB-C, HDMI", Color = "Đen",
                    IsFeatured = false, CategoryId = 1, BrandId = 6, CreatedAt = seedDate
                },
                new Laptop
                {
                    Id = 7, Name = "MacBook Air M2", Model = "2022",
                    Description = "Laptop Apple với chip M2 mạnh mẽ và tiết kiệm pin",
                    Price = 32990000, SalePrice = 29990000, StockQuantity = 8,
                    MainImageUrl = "https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=500&h=400&fit=crop",
                    Processor = "Apple M2 8-core", RAM = "8GB Unified Memory", Storage = "256GB SSD",
                    GraphicsCard = "Apple M2 8-core GPU", ScreenSize = "13.6 inch", Resolution = "2560x1664 Retina",
                    OperatingSystem = "macOS", Weight = "1.24kg", BatteryLife = "15-18 giờ",
                    Connectivity = "WiFi 6, Bluetooth 5.0, USB-C Thunderbolt", Color = "Bạc",
                    IsFeatured = true, CategoryId = 4, BrandId = 7, CreatedAt = seedDate
                },
                new Laptop
                {
                    Id = 8, Name = "ASUS VivoBook 15", Model = "X1500EA",
                    Description = "Laptop văn phòng giá rẻ hiệu năng ổn định",
                    Price = 12990000, SalePrice = 11990000, StockQuantity = 30,
                    MainImageUrl = "https://images.unsplash.com/photo-1588872657578-7efd1f1555ed?w=500&h=400&fit=crop",
                    Processor = "Intel Core i3-1115G4", RAM = "4GB DDR4", Storage = "256GB SSD",
                    GraphicsCard = "Intel UHD Graphics", ScreenSize = "15.6 inch", Resolution = "1920x1080",
                    OperatingSystem = "Windows 11", Weight = "1.8kg", BatteryLife = "6-8 giờ",
                    Connectivity = "WiFi 5, Bluetooth 4.2, USB-A, HDMI", Color = "Xanh",
                    IsFeatured = false, CategoryId = 2, BrandId = 1, CreatedAt = seedDate
                },
                new Laptop
                {
                    Id = 9, Name = "Dell Inspiron 15 3000", Model = "3511",
                    Description = "Laptop cơ bản cho học sinh sinh viên",
                    Price = 11990000, StockQuantity = 22,
                    MainImageUrl = "https://images.unsplash.com/photo-1611186871348-b1ce696e52c9?w=500&h=400&fit=crop",
                    Processor = "Intel Core i3-1115G4", RAM = "4GB DDR4", Storage = "256GB SSD",
                    GraphicsCard = "Intel UHD Graphics", ScreenSize = "15.6 inch", Resolution = "1366x768",
                    OperatingSystem = "Windows 11", Weight = "1.83kg", BatteryLife = "5-7 giờ",
                    Connectivity = "WiFi 5, Bluetooth 5.0, USB-A, HDMI", Color = "Đen",
                    IsFeatured = false, CategoryId = 2, BrandId = 2, CreatedAt = seedDate
                },
                new Laptop
                {
                    Id = 10, Name = "HP ZBook Studio G8", Model = "Mobile Workstation",
                    Description = "Laptop workstation chuyên nghiệp cho thiết kế đồ họa",
                    Price = 45990000, SalePrice = 42990000, StockQuantity = 5,
                    MainImageUrl = "https://images.unsplash.com/photo-1587614295999-6c1c13675117?w=500&h=400&fit=crop",
                    Processor = "Intel Core i7-11800H", RAM = "32GB DDR4", Storage = "1TB SSD NVMe",
                    GraphicsCard = "NVIDIA RTX A2000 4GB", ScreenSize = "15.6 inch", Resolution = "3840x2160 4K",
                    OperatingSystem = "Windows 11 Pro", Weight = "1.79kg", BatteryLife = "8-10 giờ",
                    Connectivity = "WiFi 6E, Bluetooth 5.2, USB-C Thunderbolt 4", Color = "Xám",
                    IsFeatured = true, CategoryId = 3, BrandId = 3, CreatedAt = seedDate
                }
            );
        }
    }
}
