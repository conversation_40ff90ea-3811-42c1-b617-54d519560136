using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using LaptopShop.Models;
using LaptopShop.Data;

namespace LaptopShop.Controllers;

public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;
    private readonly ApplicationDbContext _context;

    public HomeController(ILogger<HomeController> logger, ApplicationDbContext context)
    {
        _logger = logger;
        _context = context;
    }

    public async Task<IActionResult> Index()
    {
        var viewModel = new HomeViewModel
        {
            Categories = await _context.Categories
                .Where(c => c.IsActive)
                .OrderBy(c => c.Name)
                .ToListAsync(),

            FeaturedLaptops = await _context.Laptops
                .Include(l => l.Brand)
                .Include(l => l.Category)
                .Where(l => l.IsActive && l.IsFeatured)
                .OrderBy(l => l.Name)
                .Take(8)
                .ToListAsync(),

            NewLaptops = await _context.Laptops
                .Include(l => l.Brand)
                .Include(l => l.Category)
                .Where(l => l.IsActive)
                .OrderByDescending(l => l.CreatedAt)
                .Take(6)
                .ToListAsync(),

            PopularBrands = await _context.Brands
                .Where(b => b.IsActive)
                .OrderBy(b => b.Name)
                .Take(6)
                .ToListAsync()
        };

        return View(viewModel);
    }

    public IActionResult Privacy()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}
