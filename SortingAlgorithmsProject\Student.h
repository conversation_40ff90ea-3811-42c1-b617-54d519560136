#ifndef STUDENT_H
#define STUDENT_H

#include <string>
#include <iostream>
#include <iomanip>

class Student {
private:
    std::string studentId;      // Mã sinh viên
    std::string fullName;       // Họ và tên
    double averageScore;        // Điểm trung bình
    int birthYear;             // Năm sinh

public:
    // Constructor mặc định
    Student();
    
    // Constructor có tham số
    Student(const std::string& id, const std::string& name, double score, int year);
    
    // Copy constructor
    Student(const Student& other);
    
    // Assignment operator
    Student& operator=(const Student& other);
    
    // Destructor
    ~Student();
    
    // Getter methods
    std::string getStudentId() const;
    std::string getFullName() const;
    double getAverageScore() const;
    int getBirthYear() const;
    
    // Setter methods
    void setStudentId(const std::string& id);
    void setFullName(const std::string& name);
    void setAverageScore(double score);
    void setBirthYear(int year);
    
    // Comparison operators for sorting
    bool operator<(const Student& other) const;
    bool operator>(const Student& other) const;
    bool operator<=(const Student& other) const;
    bool operator>=(const Student& other) const;
    bool operator==(const Student& other) const;
    bool operator!=(const Student& other) const;
    
    // Input/Output operators
    friend std::istream& operator>>(std::istream& is, Student& student);
    friend std::ostream& operator<<(std::ostream& os, const Student& student);
    
    // Display methods
    void display() const;
    void displayHeader() const;
    
    // Validation methods
    bool isValidScore(double score) const;
    bool isValidYear(int year) const;
    bool isValidId(const std::string& id) const;
};

// Comparison functions for different sorting criteria
bool compareById(const Student& a, const Student& b);
bool compareByName(const Student& a, const Student& b);
bool compareByScore(const Student& a, const Student& b);
bool compareByYear(const Student& a, const Student& b);

#endif // STUDENT_H
