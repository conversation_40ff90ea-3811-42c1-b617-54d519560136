@model LaptopShop.Models.AdminDashboardViewModel
@{
    ViewData["Title"] = "Dashboard Admin";
}

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 admin-sidebar">
            <div class="d-flex flex-column p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-laptop me-2"></i>LaptopShop Admin
                </h5>
                
                <ul class="nav nav-pills flex-column">
                    <li class="nav-item">
                        <a class="nav-link active" asp-action="Index">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-action="Laptops">
                            <i class="fas fa-laptop me-2"></i>Quản lý Laptop
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-action="Orders">
                            <i class="fas fa-shopping-cart me-2"></i>Quản lý Đơn hàng
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-action="Customers">
                            <i class="fas fa-users me-2"></i>Quản lý Khách hàng
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-action="Categories">
                            <i class="fas fa-tags me-2"></i>Danh mục
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-action="Brands">
                            <i class="fas fa-trademark me-2"></i>Thương hiệu
                        </a>
                    </li>
                </ul>

                <hr class="text-white">
                
                <div class="mt-auto">
                    <a class="nav-link text-light" asp-controller="Home" asp-action="Index" target="_blank">
                        <i class="fas fa-external-link-alt me-2"></i>Xem Website
                    </a>
                    <a class="nav-link text-light" asp-action="Logout">
                        <i class="fas fa-sign-out-alt me-2"></i>Đăng xuất
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <div class="p-4">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="gradient-text">Dashboard Tổng Quan</h2>
                    <div class="text-muted">
                        <i class="fas fa-calendar me-1"></i>
                        @DateTime.Now.ToString("dd/MM/yyyy HH:mm")
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-laptop fa-2x text-primary mb-2"></i>
                                <h4 class="text-primary">@Model.TotalLaptops</h4>
                                <p class="text-muted mb-0">Tổng Laptop</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card success h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-shopping-cart fa-2x text-success mb-2"></i>
                                <h4 class="text-success">@Model.TotalOrders</h4>
                                <p class="text-muted mb-0">Tổng Đơn hàng</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card warning h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x text-warning mb-2"></i>
                                <h4 class="text-warning">@Model.TotalCustomers</h4>
                                <p class="text-muted mb-0">Khách hàng</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card danger h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-money-bill-wave fa-2x text-danger mb-2"></i>
                                <h4 class="text-danger">@Model.TotalRevenue.ToString("N0") ₫</h4>
                                <p class="text-muted mb-0">Tổng Doanh thu</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Today Stats -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-calendar-day me-2"></i>Hôm nay
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-6 text-center">
                                        <h4 class="text-info">@Model.TodayOrders</h4>
                                        <p class="text-muted mb-0">Đơn hàng</p>
                                    </div>
                                    <div class="col-6 text-center">
                                        <h4 class="text-info">@Model.TodayRevenue.ToString("N0") ₫</h4>
                                        <p class="text-muted mb-0">Doanh thu</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-warning text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-exclamation-triangle me-2"></i>Cảnh báo tồn kho
                                </h6>
                            </div>
                            <div class="card-body">
                                @if (Model.LowStockLaptops.Any())
                                {
                                    <p class="text-warning mb-2">
                                        <strong>@Model.LowStockLaptops.Count</strong> sản phẩm sắp hết hàng
                                    </p>
                                    @foreach (var laptop in Model.LowStockLaptops.Take(3))
                                    {
                                        <small class="d-block text-muted">
                                            @laptop.Name - Còn @laptop.StockQuantity
                                        </small>
                                    }
                                }
                                else
                                {
                                    <p class="text-success mb-0">Tất cả sản phẩm đều còn đủ hàng</p>
                                }
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Orders & Top Products -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-clock me-2"></i>Đơn hàng gần đây
                                </h6>
                            </div>
                            <div class="card-body">
                                @if (Model.RecentOrders.Any())
                                {
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Mã đơn</th>
                                                    <th>Khách hàng</th>
                                                    <th>Tổng tiền</th>
                                                    <th>Trạng thái</th>
                                                    <th>Ngày</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach (var order in Model.RecentOrders)
                                                {
                                                    <tr>
                                                        <td>@order.OrderNumber</td>
                                                        <td>@order.CustomerName</td>
                                                        <td>@order.TotalAmount.ToString("N0") ₫</td>
                                                        <td>
                                                            <span class="badge bg-@(order.Status == LaptopShop.Models.OrderStatus.Pending ? "warning" : 
                                                                                   order.Status == LaptopShop.Models.OrderStatus.Delivered ? "success" : "primary")">
                                                                @order.Status
                                                            </span>
                                                        </td>
                                                        <td>@order.CreatedAt.ToString("dd/MM")</td>
                                                    </tr>
                                                }
                                            </tbody>
                                        </table>
                                    </div>
                                }
                                else
                                {
                                    <p class="text-muted text-center py-3">Chưa có đơn hàng nào</p>
                                }
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-star me-2"></i>Sản phẩm nổi bật
                                </h6>
                            </div>
                            <div class="card-body">
                                @if (Model.TopLaptops.Any())
                                {
                                    @foreach (var laptop in Model.TopLaptops)
                                    {
                                        <div class="d-flex align-items-center mb-3">
                                            @if (!string.IsNullOrEmpty(laptop.MainImageUrl))
                                            {
                                                <img src="@laptop.MainImageUrl" class="rounded me-2" style="width: 40px; height: 40px; object-fit: cover;" alt="@laptop.Name">
                                            }
                                            else
                                            {
                                                <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                    <i class="fas fa-laptop text-muted"></i>
                                                </div>
                                            }
                                            <div class="flex-grow-1">
                                                <h6 class="mb-0 small">@laptop.Name</h6>
                                                <small class="text-muted">@laptop.Brand.Name</small>
                                            </div>
                                        </div>
                                    }
                                }
                                else
                                {
                                    <p class="text-muted text-center">Chưa có sản phẩm nổi bật</p>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
