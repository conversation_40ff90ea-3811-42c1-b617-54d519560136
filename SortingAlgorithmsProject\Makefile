# Makefile for Sorting Algorithms Project

# Compiler and flags
CXX = g++
CXXFLAGS = -std=c++11 -Wall -Wextra -O2

# Target executable
TARGET = SortingAlgorithmsDemo

# Source files
SOURCES = main.cpp Student.cpp SortingAlgorithms.cpp

# Object files
OBJECTS = $(SOURCES:.cpp=.o)

# Header files
HEADERS = Student.h SortingAlgorithms.h SortingAlgorithms.tpp

# Default target
all: $(TARGET)

# Build the executable
$(TARGET): $(OBJECTS)
	$(CXX) $(CXXFLAGS) -o $(TARGET) $(OBJECTS)
	@echo "Build completed successfully!"
	@echo "Run with: ./$(TARGET)"

# Compile source files to object files
%.o: %.cpp $(HEADERS)
	$(CXX) $(CXXFLAGS) -c $< -o $@

# Clean build files
clean:
	rm -f $(OBJECTS) $(TARGET)
	@echo "Clean completed!"

# Run the program
run: $(TARGET)
	./$(TARGET)

# Debug build
debug: CXXFLAGS += -g -DDEBUG
debug: $(TARGET)

# Release build with optimizations
release: CXXFLAGS += -O3 -DNDEBUG
release: clean $(TARGET)

# Install (copy to system directory)
install: $(TARGET)
	cp $(TARGET) /usr/local/bin/

# Uninstall
uninstall:
	rm -f /usr/local/bin/$(TARGET)

# Create documentation
docs:
	@echo "Generating documentation..."
	@echo "See BaoCaoDoAn.md for detailed documentation"

# Test with sample data
test: $(TARGET)
	@echo "Running test with sample data..."
	@echo "6\n100\n5\n0" | ./$(TARGET)

# Help
help:
	@echo "Available targets:"
	@echo "  all      - Build the project (default)"
	@echo "  clean    - Remove build files"
	@echo "  run      - Build and run the program"
	@echo "  debug    - Build with debug information"
	@echo "  release  - Build optimized release version"
	@echo "  test     - Run automated test"
	@echo "  docs     - Generate documentation"
	@echo "  help     - Show this help message"

# Phony targets
.PHONY: all clean run debug release install uninstall docs test help
