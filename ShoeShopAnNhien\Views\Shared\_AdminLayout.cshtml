<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Admin Panel</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <link rel="stylesheet" href="~/css/admin.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
</head>
<body class="admin-body">
    <!-- Top Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="@Url.Action("Index", "Admin")">
                <i class="fas fa-cogs me-2"></i>Admin Panel - Shop Giày An Nhiên
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="@Url.Action("Index", "Home")" target="_blank">
                            <i class="fas fa-external-link-alt me-1"></i>Xem Website
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>@User.Identity!.Name
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="@Url.Action("Profile", "Account")">
                                <i class="fas fa-user me-2"></i>Thông Tin Cá Nhân
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form asp-controller="Account" asp-action="Logout" method="post" class="d-inline">
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="fas fa-sign-out-alt me-2"></i>Đăng Xuất
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="admin-wrapper">
        <!-- Sidebar -->
        <nav class="sidebar bg-light">
            <div class="sidebar-content">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link @(ViewContext.RouteData.Values["action"]?.ToString() == "Index" ? "active" : "")" 
                           href="@Url.Action("Index", "Admin")">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link @(ViewContext.RouteData.Values["action"]?.ToString() == "Products" ? "active" : "")" 
                           href="@Url.Action("Products", "Admin")">
                            <i class="fas fa-box me-2"></i>Quản Lý Sản Phẩm
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link @(ViewContext.RouteData.Values["action"]?.ToString() == "Orders" ? "active" : "")" 
                           href="@Url.Action("Orders", "Admin")">
                            <i class="fas fa-shopping-cart me-2"></i>Quản Lý Đơn Hàng
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link @(ViewContext.RouteData.Values["action"]?.ToString() == "Customers" ? "active" : "")" 
                           href="@Url.Action("Customers", "Admin")">
                            <i class="fas fa-users me-2"></i>Quản Lý Khách Hàng
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link @(ViewContext.RouteData.Values["action"]?.ToString() == "Categories" ? "active" : "")" 
                           href="@Url.Action("Categories", "Admin")">
                            <i class="fas fa-tags me-2"></i>Danh Mục
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link @(ViewContext.RouteData.Values["action"]?.ToString() == "Brands" ? "active" : "")" 
                           href="@Url.Action("Brands", "Admin")">
                            <i class="fas fa-trademark me-2"></i>Thương Hiệu
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link @(ViewContext.RouteData.Values["action"]?.ToString() == "Statistics" ? "active" : "")" 
                           href="@Url.Action("Statistics", "Admin")">
                            <i class="fas fa-chart-bar me-2"></i>Thống Kê
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <div class="container-fluid py-4">
                @RenderBody()
            </div>
        </main>
    </div>

    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    <script src="~/js/admin.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
    
    <!-- Anti-forgery token for AJAX requests -->
    <input name="__RequestVerificationToken" type="hidden" value="@Html.AntiForgeryToken().ToString().Replace("<input name=\"__RequestVerificationToken\" type=\"hidden\" value=\"", "").Replace("\" />", "")" />
</body>
</html>
