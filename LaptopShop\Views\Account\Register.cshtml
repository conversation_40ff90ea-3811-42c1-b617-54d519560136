@model LaptopShop.Models.RegisterViewModel
@{
    ViewData["Title"] = "Đăng Ký";
}

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow">
                <div class="card-header bg-success text-white text-center">
                    <h4><i class="fas fa-user-plus me-2"></i>Đăng Ký Tài Khoản</h4>
                </div>
                <div class="card-body">
                    <form asp-action="Register" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label asp-for="FullName" class="form-label">Họ và tên *</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    <input asp-for="FullName" class="form-control" placeholder="Nhập họ và tên" />
                                </div>
                                <span asp-validation-for="FullName" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Email" class="form-label">Email *</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                    <input asp-for="Email" class="form-control" placeholder="Nhập email" />
                                </div>
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="PhoneNumber" class="form-label">Số điện thoại *</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                    <input asp-for="PhoneNumber" class="form-control" placeholder="Nhập số điện thoại" />
                                </div>
                                <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Address" class="form-label">Địa chỉ</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                                <input asp-for="Address" class="form-control" placeholder="Nhập địa chỉ (tùy chọn)" />
                            </div>
                            <span asp-validation-for="Address" class="text-danger"></span>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Password" class="form-label">Mật khẩu *</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input asp-for="Password" class="form-control" placeholder="Nhập mật khẩu" />
                                </div>
                                <span asp-validation-for="Password" class="text-danger"></span>
                                <div class="form-text">Mật khẩu phải có ít nhất 6 ký tự</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="ConfirmPassword" class="form-label">Xác nhận mật khẩu *</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input asp-for="ConfirmPassword" class="form-control" placeholder="Nhập lại mật khẩu" />
                                </div>
                                <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-user-plus me-2"></i>Đăng Ký
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center">
                    <p class="mb-0">
                        Đã có tài khoản? 
                        <a asp-action="Login" asp-route-returnUrl="@ViewData["ReturnUrl"]" class="text-decoration-none">
                            Đăng nhập ngay
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
