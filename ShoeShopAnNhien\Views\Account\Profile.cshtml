@model ShoeShopAnNhien.ViewModels.ProfileViewModel
@{
    ViewData["Title"] = "Thông Tin Cá Nhân";
}

<!-- Hero Section -->
<section class="hero-section bg-primary text-white py-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-5 fw-bold mb-2">Thông Tin Cá Nhân</h1>
                <p class="lead mb-0">Quản lý thông tin tài khoản của bạn</p>
            </div>
            <div class="col-lg-4 text-end">
                <i class="fas fa-user-cog" style="font-size: 4rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
</section>

<!-- Profile Content -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 col-md-4 mb-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-4">
                        <div class="text-center mb-4">
                            <div class="avatar-placeholder bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                                <i class="fas fa-user fs-2"></i>
                            </div>
                            <h5 class="mt-3 mb-1">@Model.FullName</h5>
                            <small class="text-muted">@Model.Email</small>
                        </div>
                        
                        <nav class="nav flex-column">
                            <a class="nav-link active" href="#profile-info" data-bs-toggle="pill">
                                <i class="fas fa-user me-2"></i>Thông Tin Cá Nhân
                            </a>
                            <a class="nav-link" href="#change-password" data-bs-toggle="pill">
                                <i class="fas fa-lock me-2"></i>Đổi Mật Khẩu
                            </a>
                            <a class="nav-link" href="@Url.Action("MyOrders", "Orders")">
                                <i class="fas fa-shopping-bag me-2"></i>Đơn Hàng Của Tôi
                            </a>
                            <a class="nav-link" href="@Url.Action("Index", "Cart")">
                                <i class="fas fa-shopping-cart me-2"></i>Giỏ Hàng
                            </a>
                            <hr>
                            <form asp-action="Logout" method="post" class="d-inline">
                                <button type="submit" class="nav-link border-0 bg-transparent text-danger w-100 text-start">
                                    <i class="fas fa-sign-out-alt me-2"></i>Đăng Xuất
                                </button>
                            </form>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9 col-md-8">
                <div class="tab-content">
                    <!-- Profile Info Tab -->
                    <div class="tab-pane fade show active" id="profile-info">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-white">
                                <h5 class="mb-0 fw-bold">Cập Nhật Thông Tin Cá Nhân</h5>
                            </div>
                            <div class="card-body p-4">
                                @if (TempData["SuccessMessage"] != null)
                                {
                                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                                        @TempData["SuccessMessage"]
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                }

                                <form asp-action="Profile" method="post">
                                    <div asp-validation-summary="All" class="text-danger mb-3"></div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label asp-for="FullName" class="form-label"></label>
                                            <input asp-for="FullName" class="form-control">
                                            <span asp-validation-for="FullName" class="text-danger"></span>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label asp-for="Email" class="form-label"></label>
                                            <input asp-for="Email" class="form-control" readonly>
                                            <small class="form-text text-muted">Email không thể thay đổi</small>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label asp-for="PhoneNumber" class="form-label"></label>
                                            <input asp-for="PhoneNumber" class="form-control">
                                            <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label asp-for="DateOfBirth" class="form-label"></label>
                                            <input asp-for="DateOfBirth" class="form-control" type="date">
                                            <span asp-validation-for="DateOfBirth" class="text-danger"></span>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label asp-for="Gender" class="form-label"></label>
                                            <select asp-for="Gender" class="form-select">
                                                <option value="">Chọn giới tính</option>
                                                <option value="Nam">Nam</option>
                                                <option value="Nữ">Nữ</option>
                                                <option value="Khác">Khác</option>
                                            </select>
                                            <span asp-validation-for="Gender" class="text-danger"></span>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label asp-for="Address" class="form-label"></label>
                                            <input asp-for="Address" class="form-control">
                                            <span asp-validation-for="Address" class="text-danger"></span>
                                        </div>
                                    </div>

                                    <div class="text-end">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Cập Nhật Thông Tin
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Change Password Tab -->
                    <div class="tab-pane fade" id="change-password">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-white">
                                <h5 class="mb-0 fw-bold">Đổi Mật Khẩu</h5>
                            </div>
                            <div class="card-body p-4">
                                <form asp-action="ChangePassword" method="post">
                                    <div class="mb-3">
                                        <label for="CurrentPassword" class="form-label">Mật Khẩu Hiện Tại</label>
                                        <input type="password" class="form-control" id="CurrentPassword" name="CurrentPassword" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="NewPassword" class="form-label">Mật Khẩu Mới</label>
                                        <input type="password" class="form-control" id="NewPassword" name="NewPassword" required>
                                        <small class="form-text text-muted">Mật khẩu phải có ít nhất 6 ký tự</small>
                                    </div>

                                    <div class="mb-3">
                                        <label for="ConfirmPassword" class="form-label">Xác Nhận Mật Khẩu Mới</label>
                                        <input type="password" class="form-control" id="ConfirmPassword" name="ConfirmPassword" required>
                                    </div>

                                    <div class="text-end">
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-key me-2"></i>Đổi Mật Khẩu
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
