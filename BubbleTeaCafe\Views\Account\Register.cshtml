@model RegisterViewModel
@{
    ViewData["Title"] = "Đăng Ký";
}

<div class="auth-container">
    <div class="auth-card">
        <div class="auth-header">
            <h2>🧋 Đăng Ký Thành Viên</h2>
            <p>Tạo tài khoản để nhận ưu đãi đặc biệt!</p>
        </div>

        <form asp-action="Register" method="post" class="auth-form">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            
            <div class="form-group">
                <label asp-for="FullName" class="form-label"></label>
                <input asp-for="FullName" class="form-control" placeholder="Nhập họ và tên" />
                <span asp-validation-for="FullName" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="Email" class="form-label"></label>
                <input asp-for="Email" class="form-control" placeholder="Nhập email của bạn" />
                <span asp-validation-for="Email" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="PhoneNumber" class="form-label"></label>
                <input asp-for="PhoneNumber" class="form-control" placeholder="Nhập số điện thoại" />
                <span asp-validation-for="PhoneNumber" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="Password" class="form-label"></label>
                <input asp-for="Password" class="form-control" placeholder="Tạo mật khẩu (ít nhất 6 ký tự)" />
                <span asp-validation-for="Password" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="ConfirmPassword" class="form-label"></label>
                <input asp-for="ConfirmPassword" class="form-control" placeholder="Nhập lại mật khẩu" />
                <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
            </div>

            <button type="submit" class="btn btn-primary btn-auth">
                <i class="fas fa-user-plus"></i> Đăng Ký
            </button>
        </form>

        <div class="auth-footer">
            <p>Đã có tài khoản? <a asp-action="Login" class="auth-link">Đăng nhập ngay</a></p>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
