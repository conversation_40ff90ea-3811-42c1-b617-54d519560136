html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
  text-align: start;
}

/* Custom styles for Bubble Tea & Coffee Shop */
:root {
  --primary-color: #667eea;
  --secondary-color: #764ba2;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --shadow-light: 0 2px 8px rgba(0,0,0,0.1);
  --shadow-medium: 0 4px 15px rgba(0,0,0,0.15);
  --shadow-heavy: 0 8px 25px rgba(0,0,0,0.2);
  --border-radius: 12px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Body improvements */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--dark-color);
}

/* Navbar improvements */
.navbar {
  backdrop-filter: blur(10px);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
  box-shadow: var(--shadow-light);
  transition: var(--transition);
  padding: 1.5rem 0;
  font-size: 1.1rem;
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.5rem;
  transition: var(--transition);
}

.navbar-brand:hover {
  transform: scale(1.05);
}

.nav-link {
  font-weight: 500;
  transition: var(--transition);
  position: relative;
}

.nav-link::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 50%;
  background-color: white;
  transition: var(--transition);
}

.nav-link:hover::after {
  width: 100%;
  left: 0;
}

/* Product card improvements */
.product-card {
  transition: var(--transition);
  border: none;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  overflow: hidden;
  background: white;
}

.product-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-heavy);
}

.product-image {
  height: 220px;
  object-fit: cover;
  transition: var(--transition);
}

.product-card:hover .product-image {
  transform: scale(1.1);
}

.product-image-placeholder {
  height: 220px;
  background: linear-gradient(45deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  transition: var(--transition);
}

.product-card:hover .product-image-placeholder {
  background: linear-gradient(45deg, #e9ecef 0%, #dee2e6 100%);
}

.price-tag {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--success-color);
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.product-image-container {
  position: relative;
  overflow: hidden;
  height: 220px;
}

.product-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(108, 99, 255, 0.9), rgba(255, 107, 107, 0.9));
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-card:hover .product-overlay {
  opacity: 1;
}

.price-badge {
  background: white;
  color: var(--primary-color);
  padding: 10px 20px;
  border-radius: 25px;
  font-weight: bold;
  font-size: 1.2rem;
  box-shadow: 0 4px 15px rgba(0,0,0,0.3);
  transform: translateY(10px);
  transition: transform 0.3s ease;
}

.product-card:hover .price-badge {
  transform: translateY(0);
}

/* Gallery Section */
.gallery-item {
  position: relative;
  overflow: hidden;
  border-radius: 15px;
  box-shadow: var(--shadow-light);
  transition: all 0.3s ease;
}

.gallery-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-heavy);
}

.gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.gallery-item:hover img {
  transform: scale(1.05);
}

.gallery-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0,0,0,0.8));
  color: white;
  padding: 20px;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
  transform: translateY(0);
}

.gallery-main {
  height: 400px;
}

.gallery-sub {
  height: 180px;
}

@media (min-width: 1200px) {
  .gallery-main {
    height: 500px;
  }

  .gallery-sub {
    height: 230px;
  }
}

/* Button improvements */
.btn {
  border-radius: 8px;
  font-weight: 500;
  transition: var(--transition);
  border: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: var(--transition);
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  box-shadow: var(--shadow-light);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.btn-outline-primary {
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
}

.btn-outline-primary:hover {
  background: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

/* Section improvements */
.category-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: white;
  padding: 3rem 0;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-medium);
  position: relative;
  overflow: hidden;
}

.category-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('/images/pattern.png');
  opacity: 0.1;
}

.hero-section {
  background: linear-gradient(135deg, rgba(108, 99, 255, 0.9) 0%, rgba(255, 107, 107, 0.9) 100%),
              url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  color: white;
  padding: 8rem 0;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-medium);
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero-content {
  max-width: 100%;
}

.hero-title {
  font-size: 4rem;
  line-height: 1.1;
  margin-bottom: 2rem;
}

.hero-subtitle {
  font-size: 1.4rem;
  line-height: 1.6;
  max-width: 600px;
}

.hero-buttons .btn {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
}

/* Feature Cards */
.feature-card {
  transition: all 0.3s ease;
  border-radius: 20px;
  overflow: hidden;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.1) !important;
}

.feature-icon {
  transition: transform 0.3s ease;
}

.feature-card:hover .feature-icon {
  transform: scale(1.1);
}

/* CTA Section */
.cta-section {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="3" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
  animation: float 20s infinite linear;
}

.cta-btn {
  padding: 1.2rem 2.5rem;
  font-size: 1.2rem;
  font-weight: 600;
  border-radius: 50px;
  transition: all 0.3s ease;
  border-width: 2px;
}

.cta-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  z-index: 1;
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.hero-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: particleFloat 8s infinite linear;
}

.particle:nth-child(1) {
  left: 10%;
  animation-delay: 0s;
  animation-duration: 8s;
}

.particle:nth-child(2) {
  left: 30%;
  animation-delay: 2s;
  animation-duration: 10s;
}

.particle:nth-child(3) {
  left: 50%;
  animation-delay: 4s;
  animation-duration: 12s;
}

.particle:nth-child(4) {
  left: 70%;
  animation-delay: 6s;
  animation-duration: 9s;
}

.particle:nth-child(5) {
  left: 90%;
  animation-delay: 8s;
  animation-duration: 11s;
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) scale(1);
    opacity: 0;
  }
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
  opacity: 0.1;
  animation: gradientShift 10s ease-in-out infinite;
}

.min-vh-75 {
  min-height: 75vh;
}

.text-shadow {
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero-icon-container {
  position: relative;
  display: inline-block;
}

.hero-icon {
  font-size: 15rem;
  opacity: 0.8;
  animation: float 3s ease-in-out infinite;
  color: rgba(255,255,255,0.9);
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.floating-element {
  position: absolute;
  font-size: 2rem;
  animation: floatAround 8s linear infinite;
  opacity: 0.7;
}

.floating-element:nth-child(1) {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.floating-element:nth-child(2) {
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.floating-element:nth-child(3) {
  bottom: 30%;
  left: 20%;
  animation-delay: 4s;
}

.floating-element:nth-child(4) {
  top: 40%;
  right: 30%;
  animation-delay: 6s;
}

.pulse-animation {
  animation: pulse 2s infinite;
}

/* Animation keyframes */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes floatAround {
  0% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-10px) rotate(90deg); }
  50% { transform: translateY(0px) rotate(180deg); }
  75% { transform: translateY(-10px) rotate(270deg); }
  100% { transform: translateY(0px) rotate(360deg); }
}

@keyframes gradientShift {
  0%, 100% { background: linear-gradient(45deg, var(--primary-color), var(--secondary-color)); }
  50% { background: linear-gradient(45deg, var(--secondary-color), var(--primary-color)); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Cart badge */
.cart-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  transition: var(--transition);
}



/* Product options */
.product-options {
  background-color: var(--light-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  margin: 1.5rem 0;
  box-shadow: var(--shadow-light);
  transition: var(--transition);
}

.product-options:hover {
  box-shadow: var(--shadow-medium);
}

.option-group {
  margin-bottom: 1.5rem;
}

.option-group label {
  font-weight: 600;
  color: var(--dark-color);
}

/* Card improvements */
.card {
  border-radius: var(--border-radius);
  border: none;
  box-shadow: var(--shadow-light);
  transition: var(--transition);
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-medium);
}

.card-header {
  border-bottom: none;
  background-color: white;
  font-weight: 600;
}

/* Form improvements */
.form-control, .form-select {
  border-radius: 8px;
  border: 1px solid #dee2e6;
  padding: 0.6rem 1rem;
  transition: var(--transition);
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
}

.form-check-input:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

/* Loading spinner */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255,255,255,.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

/* Smooth page transitions */
.page-transition {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s ease-out;
}

.page-transition.loaded {
  opacity: 1;
  transform: translateY(0);
}

/* Enhanced card hover effects */
.card-hover-effect {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover-effect:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

/* Smooth scroll indicator */
.scroll-indicator {
  position: fixed;
  top: 0;
  left: 0;
  width: 0%;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  z-index: 9999;
  transition: width 0.3s ease;
}

/* Animation keyframes */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes slideInLeft {
  from { opacity: 0; transform: translateX(-50px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInRight {
  from { opacity: 0; transform: translateX(50px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes bounceIn {
  0% { opacity: 0; transform: scale(0.3); }
  50% { opacity: 1; transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { opacity: 1; transform: scale(1); }
}

.fade-in {
  animation: fadeIn 0.8s ease-out forwards;
}

.slide-in-left {
  animation: slideInLeft 0.8s ease-out forwards;
}

.slide-in-right {
  animation: slideInRight 0.8s ease-out forwards;
}

.bounce-in {
  animation: bounceIn 0.8s ease-out forwards;
}

/* Desktop optimizations */
@media (min-width: 1200px) {
  .hero-section {
    min-height: 100vh;
    padding: 10rem 0;
  }

  .hero-section h1 {
    font-size: 4.5rem;
    line-height: 1.2;
  }

  .hero-section .lead {
    font-size: 1.5rem;
    max-width: 600px;
  }

  .container {
    max-width: 1400px;
  }

  .product-card {
    transition: all 0.4s ease;
  }

  .product-card:hover {
    transform: translateY(-15px) scale(1.03);
  }

  .gallery-item {
    height: 300px;
  }

  .gallery-item:first-child {
    height: 620px;
  }

  .section-title {
    font-size: 3.5rem;
  }

  .display-5 {
    font-size: 3rem;
  }
}

@media (min-width: 992px) and (max-width: 1199px) {
  .hero-section {
    min-height: 90vh;
    padding: 8rem 0;
  }

  .hero-section h1 {
    font-size: 3.5rem;
  }

  .gallery-item {
    height: 250px;
  }

  .gallery-item:first-child {
    height: 520px;
  }
}

/* Responsive improvements */
@media (max-width: 768px) {
  .hero-section {
    padding: 5rem 0;
  }

  .product-card {
    margin-bottom: 1.5rem;
  }

  .auth-card {
    padding: 2rem;
    margin: 0 0.5rem;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Authentication Styles */
.auth-container {
  min-height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.auth-card {
  background: white;
  border-radius: 20px;
  padding: 3rem;
  box-shadow: var(--shadow-large);
  width: 100%;
  max-width: 450px;
  margin: 0 1rem;
}

.auth-header {
  text-align: center;
  margin-bottom: 2rem;
}

.auth-header h2 {
  color: var(--primary-color);
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.auth-header p {
  color: var(--text-secondary);
  margin: 0;
}

.auth-form .form-group {
  margin-bottom: 1.5rem;
}

.auth-form .form-label {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  display: block;
}

.auth-form .form-control {
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.auth-form .form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(108, 99, 255, 0.25);
}

.form-check {
  margin: 1.5rem 0;
}

.form-check-input {
  margin-right: 0.5rem;
}

.btn-auth {
  width: 100%;
  padding: 0.875rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 12px;
  margin-top: 1rem;
}

.auth-footer {
  text-align: center;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e1e5e9;
}

.auth-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 600;
}

.auth-link:hover {
  text-decoration: underline;
}

.forgot-password {
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 0.9rem;
  display: block;
  margin-top: 1rem;
}

.forgot-password:hover {
  color: var(--primary-color);
}