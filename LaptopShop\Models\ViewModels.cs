using System.ComponentModel.DataAnnotations;

namespace LaptopShop.Models
{
    // ViewModels cho Authentication
    public class LoginViewModel
    {
        [Required(ErrorMessage = "Email là bắt buộc")]
        [EmailAddress(ErrorMessage = "Email không hợp lệ")]
        public string Email { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Mật khẩu là bắt buộc")]
        [DataType(DataType.Password)]
        public string Password { get; set; } = string.Empty;
        
        public bool RememberMe { get; set; }
    }

    public class RegisterViewModel
    {
        [Required(ErrorMessage = "Họ tên là bắt buộc")]
        [StringLength(100)]
        public string FullName { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Email là bắt buộc")]
        [EmailAddress(ErrorMessage = "<PERSON>ail không hợp lệ")]
        public string Email { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Số điện thoại là bắt buộc")]
        [Phone(ErrorMessage = "Số điện thoại không hợp lệ")]
        public string PhoneNumber { get; set; } = string.Empty;
        
        [StringLength(200)]
        public string? Address { get; set; }
        
        [Required(ErrorMessage = "Mật khẩu là bắt buộc")]
        [StringLength(100, ErrorMessage = "Mật khẩu phải có ít nhất {2} ký tự", MinimumLength = 6)]
        [DataType(DataType.Password)]
        public string Password { get; set; } = string.Empty;
        
        [DataType(DataType.Password)]
        [Compare("Password", ErrorMessage = "Mật khẩu xác nhận không khớp")]
        public string ConfirmPassword { get; set; } = string.Empty;
    }

    // ViewModels cho Home
    public class HomeViewModel
    {
        public List<Category> Categories { get; set; } = new();
        public List<Laptop> FeaturedLaptops { get; set; } = new();
        public List<Laptop> NewLaptops { get; set; } = new();
        public List<Brand> PopularBrands { get; set; } = new();
    }

    // ViewModels cho Product
    public class ProductListViewModel
    {
        public List<Laptop> Laptops { get; set; } = new();
        public List<Category> Categories { get; set; } = new();
        public List<Brand> Brands { get; set; } = new();
        public string? SearchTerm { get; set; }
        public int? CategoryId { get; set; }
        public int? BrandId { get; set; }
        public decimal? MinPrice { get; set; }
        public decimal? MaxPrice { get; set; }
        public string SortBy { get; set; } = "name";
        public int CurrentPage { get; set; } = 1;
        public int TotalPages { get; set; }
        public int TotalItems { get; set; }
        public int PageSize { get; set; } = 12;
    }

    public class ProductDetailViewModel
    {
        public Laptop Laptop { get; set; } = null!;
        public List<Laptop> RelatedLaptops { get; set; } = new();
        public List<Review> Reviews { get; set; } = new();
        public double AverageRating { get; set; }
        public int TotalReviews { get; set; }
        public bool CanReview { get; set; }
    }

    // ViewModels cho Cart
    public class CartViewModel
    {
        public List<CartItemViewModel> Items { get; set; } = new();
        public decimal SubTotal { get; set; }
        public decimal ShippingFee { get; set; }
        public decimal Total { get; set; }
        public int TotalItems { get; set; }
    }

    public class CartItemViewModel
    {
        public int Id { get; set; }
        public int LaptopId { get; set; }
        public string LaptopName { get; set; } = string.Empty;
        public string? LaptopImage { get; set; }
        public decimal Price { get; set; }
        public decimal? SalePrice { get; set; }
        public int Quantity { get; set; }
        public decimal Total { get; set; }
        public int StockQuantity { get; set; }
    }

    // ViewModels cho Checkout
    public class CheckoutViewModel
    {
        public CartViewModel Cart { get; set; } = new();
        
        [Required(ErrorMessage = "Họ tên là bắt buộc")]
        [StringLength(100)]
        public string CustomerName { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Email là bắt buộc")]
        [EmailAddress]
        public string CustomerEmail { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Số điện thoại là bắt buộc")]
        [Phone]
        public string CustomerPhone { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Địa chỉ giao hàng là bắt buộc")]
        [StringLength(500)]
        public string ShippingAddress { get; set; } = string.Empty;
        
        [StringLength(1000)]
        public string? Notes { get; set; }
        
        [Required(ErrorMessage = "Phương thức thanh toán là bắt buộc")]
        public string PaymentMethod { get; set; } = string.Empty;
    }

    // ViewModels cho Admin
    public class AdminDashboardViewModel
    {
        public int TotalLaptops { get; set; }
        public int TotalOrders { get; set; }
        public int TotalCustomers { get; set; }
        public decimal TotalRevenue { get; set; }
        public int TodayOrders { get; set; }
        public decimal TodayRevenue { get; set; }
        public List<Order> RecentOrders { get; set; } = new();
        public List<Laptop> TopLaptops { get; set; } = new();
        public List<Laptop> LowStockLaptops { get; set; } = new();
    }

    public class AdminLoginViewModel
    {
        [Required(ErrorMessage = "Tên đăng nhập là bắt buộc")]
        public string Username { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Mật khẩu là bắt buộc")]
        [DataType(DataType.Password)]
        public string Password { get; set; } = string.Empty;
        
        public bool RememberMe { get; set; }
    }
}
