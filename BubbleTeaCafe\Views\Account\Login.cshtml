@model LoginViewModel
@{
    ViewData["Title"] = "Đăng Nhập";
}

<div class="auth-container">
    <div class="auth-card">
        <div class="auth-header">
            <h2>🧋 Đăng Nhập</h2>
            <p>Chào mừng bạn quay trở lại!</p>
        </div>

        <form asp-action="Login" method="post" class="auth-form">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            
            <div class="form-group">
                <label asp-for="Email" class="form-label"></label>
                <input asp-for="Email" class="form-control" placeholder="Nhập email của bạn" />
                <span asp-validation-for="Email" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="Password" class="form-label"></label>
                <input asp-for="Password" class="form-control" placeholder="Nhập mật khẩu" />
                <span asp-validation-for="Password" class="text-danger"></span>
            </div>

            <div class="form-check">
                <input asp-for="RememberMe" class="form-check-input" />
                <label asp-for="RememberMe" class="form-check-label"></label>
            </div>

            <button type="submit" class="btn btn-primary btn-auth">
                <i class="fas fa-sign-in-alt"></i> Đăng Nhập
            </button>
        </form>

        <div class="auth-footer">
            <p>Chưa có tài khoản? <a asp-action="Register" class="auth-link">Đăng ký ngay</a></p>
            <a href="#" class="forgot-password">Quên mật khẩu?</a>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
