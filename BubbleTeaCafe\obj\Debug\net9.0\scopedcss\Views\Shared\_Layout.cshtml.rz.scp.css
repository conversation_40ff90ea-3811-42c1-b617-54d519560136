/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-dnhkx54j9d] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-dnhkx54j9d] {
  color: #0077cc;
}

.btn-primary[b-dnhkx54j9d] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-dnhkx54j9d], .nav-pills .show > .nav-link[b-dnhkx54j9d] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-dnhkx54j9d] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-dnhkx54j9d] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-dnhkx54j9d] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-dnhkx54j9d] {
  font-size: 1rem;
  line-height: inherit;
}


