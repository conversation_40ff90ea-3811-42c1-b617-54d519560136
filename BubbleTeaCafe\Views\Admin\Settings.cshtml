@{
    ViewData["Title"] = "Cài Đặt Hệ Thống";
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-cogs me-2 text-primary"></i>Cài Đặt Hệ Thống
                </h1>
                <button class="btn btn-success" onclick="saveAllSettings()">
                    <i class="fas fa-save me-2"></i>L<PERSON>u Tất <PERSON>
                </button>
            </div>

            <div class="row">
                <!-- General Settings -->
                <div class="col-lg-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-store me-2"></i>Thông Tin Cửa Hàng
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="storeSettingsForm">
                                <div class="mb-3">
                                    <label class="form-label">Tên cửa hàng</label>
                                    <input type="text" class="form-control" value="Bubble Tea & Coffee Shop">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Địa chỉ</label>
                                    <textarea class="form-control" rows="2">123 Đường ABC, Phường XYZ, Quận 1, TP.HCM</textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Số điện thoại</label>
                                    <input type="tel" class="form-control" value="0123 456 789">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Email</label>
                                    <input type="email" class="form-control" value="<EMAIL>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Website</label>
                                    <input type="url" class="form-control" value="https://bubbletea.com">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Mô tả</label>
                                    <textarea class="form-control" rows="3">Thưởng thức những ly trà sữa và cà phê tuyệt vời nhất tại cửa hàng của chúng tôi.</textarea>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Business Hours -->
                <div class="col-lg-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-clock me-2"></i>Giờ Mở Cửa
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="hoursSettingsForm">
                                <div class="mb-3">
                                    <label class="form-label">Thứ 2 - Thứ 6</label>
                                    <div class="row">
                                        <div class="col-6">
                                            <input type="time" class="form-control" value="07:00">
                                        </div>
                                        <div class="col-6">
                                            <input type="time" class="form-control" value="22:00">
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Thứ 7 - Chủ Nhật</label>
                                    <div class="row">
                                        <div class="col-6">
                                            <input type="time" class="form-control" value="08:00">
                                        </div>
                                        <div class="col-6">
                                            <input type="time" class="form-control" value="23:00">
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Ngày lễ</label>
                                    <div class="row">
                                        <div class="col-6">
                                            <input type="time" class="form-control" value="09:00">
                                        </div>
                                        <div class="col-6">
                                            <input type="time" class="form-control" value="21:00">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="autoClose">
                                    <label class="form-check-label" for="autoClose">
                                        Tự động đóng cửa khi hết giờ
                                    </label>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Payment Settings -->
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-credit-card me-2"></i>Cài Đặt Thanh Toán
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="paymentSettingsForm">
                                <div class="mb-3">
                                    <label class="form-label">Phương thức thanh toán</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="cashPayment" checked>
                                        <label class="form-check-label" for="cashPayment">
                                            Tiền mặt
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="cardPayment" checked>
                                        <label class="form-check-label" for="cardPayment">
                                            Thẻ tín dụng/ghi nợ
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="digitalPayment" checked>
                                        <label class="form-check-label" for="digitalPayment">
                                            Ví điện tử (Momo, ZaloPay, VNPay)
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="bankTransfer">
                                        <label class="form-check-label" for="bankTransfer">
                                            Chuyển khoản ngân hàng
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Thuế VAT (%)</label>
                                    <input type="number" class="form-control" value="10" min="0" max="100">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Phí dịch vụ (%)</label>
                                    <input type="number" class="form-control" value="0" min="0" max="100">
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Notification Settings -->
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-bell me-2"></i>Cài Đặt Thông Báo
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="notificationSettingsForm">
                                <div class="mb-3">
                                    <label class="form-label">Email thông báo</label>
                                    <input type="email" class="form-control" value="<EMAIL>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Thông báo đơn hàng mới</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="emailNewOrder" checked>
                                        <label class="form-check-label" for="emailNewOrder">
                                            Gửi email
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="smsNewOrder">
                                        <label class="form-check-label" for="smsNewOrder">
                                            Gửi SMS
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Thông báo hết hàng</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="emailOutOfStock" checked>
                                        <label class="form-check-label" for="emailOutOfStock">
                                            Gửi email khi sản phẩm hết hàng
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Báo cáo hàng ngày</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="dailyReport" checked>
                                        <label class="form-check-label" for="dailyReport">
                                            Gửi báo cáo doanh thu hàng ngày
                                        </label>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- System Settings -->
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-server me-2"></i>Cài Đặt Hệ Thống
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="systemSettingsForm">
                                <div class="mb-3">
                                    <label class="form-label">Múi giờ</label>
                                    <select class="form-select">
                                        <option value="Asia/Ho_Chi_Minh" selected>Việt Nam (UTC+7)</option>
                                        <option value="Asia/Bangkok">Thái Lan (UTC+7)</option>
                                        <option value="Asia/Singapore">Singapore (UTC+8)</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Ngôn ngữ</label>
                                    <select class="form-select">
                                        <option value="vi" selected>Tiếng Việt</option>
                                        <option value="en">English</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Đơn vị tiền tệ</label>
                                    <select class="form-select">
                                        <option value="VND" selected>VND (đ)</option>
                                        <option value="USD">USD ($)</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Số sản phẩm trên trang</label>
                                    <input type="number" class="form-control" value="12" min="1" max="100">
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="maintenanceMode">
                                    <label class="form-check-label" for="maintenanceMode">
                                        Chế độ bảo trì
                                    </label>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Security Settings -->
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-shield-alt me-2"></i>Cài Đặt Bảo Mật
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="securitySettingsForm">
                                <div class="mb-3">
                                    <label class="form-label">Thời gian hết phiên (phút)</label>
                                    <input type="number" class="form-control" value="30" min="5" max="1440">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Số lần đăng nhập sai tối đa</label>
                                    <input type="number" class="form-control" value="5" min="1" max="10">
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="twoFactorAuth">
                                    <label class="form-check-label" for="twoFactorAuth">
                                        Xác thực 2 bước
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="forcePasswordChange" checked>
                                    <label class="form-check-label" for="forcePasswordChange">
                                        Bắt buộc đổi mật khẩu định kỳ (90 ngày)
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="loginNotification" checked>
                                    <label class="form-check-label" for="loginNotification">
                                        Thông báo khi đăng nhập từ thiết bị mới
                                    </label>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center">
                            <button class="btn btn-success btn-lg me-3" onclick="saveAllSettings()">
                                <i class="fas fa-save me-2"></i>Lưu Tất Cả Cài Đặt
                            </button>
                            <button class="btn btn-warning btn-lg me-3" onclick="resetToDefault()">
                                <i class="fas fa-undo me-2"></i>Khôi Phục Mặc Định
                            </button>
                            <button class="btn btn-info btn-lg" onclick="exportSettings()">
                                <i class="fas fa-download me-2"></i>Xuất Cài Đặt
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function saveAllSettings() {
            if (confirm('Bạn có chắc chắn muốn lưu tất cả cài đặt?')) {
                // Simulate saving
                alert('Đã lưu tất cả cài đặt thành công!');
            }
        }

        function resetToDefault() {
            if (confirm('Bạn có chắc chắn muốn khôi phục về cài đặt mặc định? Tất cả thay đổi sẽ bị mất!')) {
                alert('Đã khôi phục về cài đặt mặc định!');
                location.reload();
            }
        }

        function exportSettings() {
            alert('Xuất file cài đặt thành công!');
        }

        // Auto-save functionality
        document.querySelectorAll('input, select, textarea').forEach(element => {
            element.addEventListener('change', function() {
                console.log('Setting changed:', this.name || this.id, this.value);
                // Auto-save logic here
            });
        });
    </script>
}
