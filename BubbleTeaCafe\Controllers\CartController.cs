using Microsoft.AspNetCore.Mvc;
using BubbleTeaCafe.Data;
using BubbleTeaCafe.Models;
using BubbleTeaCafe.Models.ViewModels;
using BubbleTeaCafe.Services;
using Microsoft.EntityFrameworkCore;

namespace BubbleTeaCafe.Controllers;

public class CartController : Controller
{
    private readonly BubbleTeaCafeContext _context;
    private readonly ICartService _cartService;

    public CartController(BubbleTeaCafeContext context, ICartService cartService)
    {
        _context = context;
        _cartService = cartService;
    }

    // GET: Cart
    public IActionResult Index()
    {
        var cart = _cartService.GetCart(HttpContext.Session);
        
        // Load product details for each cart item
        foreach (var item in cart.Items)
        {
            var product = _context.Products.FirstOrDefault(p => p.ProductId == item.ProductId);
            if (product != null)
            {
                item.Product = product;
            }
        }
        
        return View(cart);
    }

    // POST: Cart/AddToCart
    [HttpPost]
    public async Task<IActionResult> AddToCart(AddToCartViewModel model)
    {
        if (ModelState.IsValid)
        {
            var product = await _context.Products.FindAsync(model.ProductId);
            if (product == null)
            {
                return NotFound();
            }

            var cartItem = new CartItem
            {
                ProductId = model.ProductId,
                Quantity = model.Quantity,
                Size = model.Size,
                Temperature = model.Temperature,
                Sweetness = model.Sweetness,
                SpecialInstructions = model.SpecialInstructions,
                UnitPrice = product.Price,
                Product = product
            };

            _cartService.AddToCart(HttpContext.Session, cartItem);
            
            return RedirectToAction(nameof(Index));
        }
        
        return RedirectToAction("Details", "Product", new { id = model.ProductId });
    }

    // POST: Cart/RemoveFromCart
    [HttpPost]
    public IActionResult RemoveFromCart(int cartItemId)
    {
        _cartService.RemoveFromCart(HttpContext.Session, cartItemId);
        return RedirectToAction(nameof(Index));
    }

    // POST: Cart/UpdateQuantity
    [HttpPost]
    public IActionResult UpdateQuantity(int cartItemId, int quantity)
    {
        if (quantity > 0)
        {
            _cartService.UpdateQuantity(HttpContext.Session, cartItemId, quantity);
        }
        return RedirectToAction(nameof(Index));
    }

    // POST: Cart/ClearCart
    [HttpPost]
    public IActionResult ClearCart()
    {
        _cartService.ClearCart(HttpContext.Session);
        return RedirectToAction(nameof(Index));
    }
}
