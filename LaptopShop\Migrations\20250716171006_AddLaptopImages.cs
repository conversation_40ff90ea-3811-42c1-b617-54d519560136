﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LaptopShop.Migrations
{
    /// <inheritdoc />
    public partial class AddLaptopImages : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 1,
                column: "MainImageUrl",
                value: "https://images.unsplash.com/photo-1603302576837-37561b2e2302?w=500&h=400&fit=crop");

            migrationBuilder.UpdateData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 2,
                column: "MainImageUrl",
                value: "https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=500&h=400&fit=crop");

            migrationBuilder.UpdateData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 3,
                column: "MainImageUrl",
                value: "https://images.unsplash.com/photo-1525547719571-a2d4ac8945e2?w=500&h=400&fit=crop");

            migrationBuilder.UpdateData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 4,
                column: "MainImageUrl",
                value: "https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=500&h=400&fit=crop");

            migrationBuilder.UpdateData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 5,
                column: "MainImageUrl",
                value: "https://images.unsplash.com/photo-1484788984921-03950022c9ef?w=500&h=400&fit=crop");

            migrationBuilder.UpdateData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 6,
                column: "MainImageUrl",
                value: "https://images.unsplash.com/photo-1593642702821-c8da6771f0c6?w=500&h=400&fit=crop");

            migrationBuilder.UpdateData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 7,
                column: "MainImageUrl",
                value: "https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=500&h=400&fit=crop");

            migrationBuilder.UpdateData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 8,
                column: "MainImageUrl",
                value: "https://images.unsplash.com/photo-1588872657578-7efd1f1555ed?w=500&h=400&fit=crop");

            migrationBuilder.UpdateData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 9,
                column: "MainImageUrl",
                value: "https://images.unsplash.com/photo-1611186871348-b1ce696e52c9?w=500&h=400&fit=crop");

            migrationBuilder.UpdateData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 10,
                column: "MainImageUrl",
                value: "https://images.unsplash.com/photo-1587614295999-6c1c13675117?w=500&h=400&fit=crop");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 1,
                column: "MainImageUrl",
                value: null);

            migrationBuilder.UpdateData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 2,
                column: "MainImageUrl",
                value: null);

            migrationBuilder.UpdateData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 3,
                column: "MainImageUrl",
                value: null);

            migrationBuilder.UpdateData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 4,
                column: "MainImageUrl",
                value: null);

            migrationBuilder.UpdateData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 5,
                column: "MainImageUrl",
                value: null);

            migrationBuilder.UpdateData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 6,
                column: "MainImageUrl",
                value: null);

            migrationBuilder.UpdateData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 7,
                column: "MainImageUrl",
                value: null);

            migrationBuilder.UpdateData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 8,
                column: "MainImageUrl",
                value: null);

            migrationBuilder.UpdateData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 9,
                column: "MainImageUrl",
                value: null);

            migrationBuilder.UpdateData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 10,
                column: "MainImageUrl",
                value: null);
        }
    }
}
