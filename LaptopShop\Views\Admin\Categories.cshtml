@model List<LaptopShop.Models.Category>
@{
    ViewData["Title"] = "Quản Lý Dan<PERSON> Mục";
}

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 admin-sidebar">
            <div class="d-flex flex-column p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-laptop me-2"></i>LaptopShop Admin
                </h5>
                
                <ul class="nav nav-pills flex-column">
                    <li class="nav-item">
                        <a class="nav-link" asp-action="Index">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-action="Laptops">
                            <i class="fas fa-laptop me-2"></i>Quản lý Laptop
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-action="Orders">
                            <i class="fas fa-shopping-cart me-2"></i>Quản lý Đơn hàng
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-action="Customers">
                            <i class="fas fa-users me-2"></i>Quản lý Khách hàng
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" asp-action="Categories">
                            <i class="fas fa-tags me-2"></i>Danh mục
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-action="Brands">
                            <i class="fas fa-trademark me-2"></i>Thương hiệu
                        </a>
                    </li>
                </ul>

                <hr class="text-white">
                
                <div class="mt-auto">
                    <a class="nav-link text-light" asp-controller="Home" asp-action="Index" target="_blank">
                        <i class="fas fa-external-link-alt me-2"></i>Xem Website
                    </a>
                    <a class="nav-link text-light" asp-action="Logout">
                        <i class="fas fa-sign-out-alt me-2"></i>Đăng xuất
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <div class="p-4">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="gradient-text">Quản Lý Danh Mục</h2>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                        <i class="fas fa-plus me-2"></i>Thêm Danh Mục Mới
                    </button>
                </div>

                <!-- Stats Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-tags fa-2x text-primary mb-2"></i>
                                <h4 class="text-primary">@(Model?.Count ?? 0)</h4>
                                <p class="text-muted mb-0">Tổng danh mục</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-eye fa-2x text-success mb-2"></i>
                                <h4 class="text-success">@(Model?.Count(c => c.IsActive) ?? 0)</h4>
                                <p class="text-muted mb-0">Đang hiển thị</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-eye-slash fa-2x text-warning mb-2"></i>
                                <h4 class="text-warning">@(Model?.Count(c => !c.IsActive) ?? 0)</h4>
                                <p class="text-muted mb-0">Đang ẩn</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-laptop fa-2x text-info mb-2"></i>
                                <h4 class="text-info">@(Model?.Sum(c => c.Laptops?.Count ?? 0) ?? 0)</h4>
                                <p class="text-muted mb-0">Tổng sản phẩm</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Search -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="Tìm kiếm danh mục...">
                            <button class="btn btn-outline-secondary" type="button">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select">
                            <option value="">Tất cả trạng thái</option>
                            <option value="active">Đang hiển thị</option>
                            <option value="inactive">Đang ẩn</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select">
                            <option value="">Sắp xếp theo</option>
                            <option value="name">Tên A-Z</option>
                            <option value="products">Số sản phẩm</option>
                            <option value="date">Ngày tạo</option>
                        </select>
                    </div>
                </div>

                <!-- Categories Grid -->
                <div class="row">
                    @if (Model != null && Model.Any())
                    {
                        @foreach (var category in Model)
                        {
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card h-100 category-card">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <div class="category-icon">
                                                <i class="fas fa-@(category.Name.ToLower() switch 
                                                {
                                                    "gaming" => "gamepad",
                                                    "văn phòng" => "briefcase",
                                                    "đồ họa" => "paint-brush",
                                                    "học tập" => "graduation-cap",
                                                    "mỏng nhẹ" => "feather-alt",
                                                    _ => "laptop"
                                                }) fa-2x text-primary"></i>
                                            </div>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#"><i class="fas fa-edit me-2"></i>Chỉnh sửa</a></li>
                                                    <li><a class="dropdown-item" href="#"><i class="fas fa-eye me-2"></i>Xem sản phẩm</a></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    @if (category.IsActive)
                                                    {
                                                        <li><a class="dropdown-item text-warning" href="#"><i class="fas fa-eye-slash me-2"></i>Ẩn danh mục</a></li>
                                                    }
                                                    else
                                                    {
                                                        <li><a class="dropdown-item text-success" href="#"><i class="fas fa-eye me-2"></i>Hiển thị</a></li>
                                                    }
                                                    <li><a class="dropdown-item text-danger" href="#"><i class="fas fa-trash me-2"></i>Xóa</a></li>
                                                </ul>
                                            </div>
                                        </div>
                                        
                                        <h5 class="card-title">@category.Name</h5>
                                        <p class="card-text text-muted">@category.Description</p>
                                        
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <span class="badge bg-info">@(category.Laptops?.Count ?? 0) sản phẩm</span>
                                                @if (category.IsActive)
                                                {
                                                    <span class="badge bg-success ms-1">Hiển thị</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary ms-1">Ẩn</span>
                                                }
                                            </div>
                                            <small class="text-muted">@category.CreatedAt.ToString("dd/MM/yyyy")</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="col-12">
                            <div class="text-center py-5">
                                <i class="fas fa-tags fa-5x text-muted mb-3"></i>
                                <h4 class="text-muted mb-3">Chưa có danh mục nào</h4>
                                <p class="text-muted mb-4">Hãy tạo danh mục đầu tiên cho cửa hàng của bạn!</p>
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                                    <i class="fas fa-plus me-2"></i>Thêm Danh Mục Đầu Tiên
                                </button>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Category Modal -->
<div class="modal fade" id="addCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Thêm Danh Mục Mới</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="categoryName" class="form-label">Tên danh mục</label>
                        <input type="text" class="form-control" id="categoryName" placeholder="Nhập tên danh mục">
                    </div>
                    <div class="mb-3">
                        <label for="categoryDescription" class="form-label">Mô tả</label>
                        <textarea class="form-control" id="categoryDescription" rows="3" placeholder="Nhập mô tả danh mục"></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="categoryActive" checked>
                            <label class="form-check-label" for="categoryActive">
                                Hiển thị danh mục
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary">Thêm Danh Mục</button>
            </div>
        </div>
    </div>
</div>

<style>
.category-card {
    transition: transform 0.2s ease-in-out;
    border: 1px solid #e3e6f0;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.category-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}
</style>
