{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["+AxQixWoYhyv1KwXECDvPNQgpVNWAhhc5ABlAKjZ3ZY=", "QXFjzqvrnAAtIfJ27dK4+/HXnpZMUXljiKQpBXx0mcg=", "B9OFDgoGnyvI9vEcnA5z5g4XMbdHZaBcv0r+qks1vtI=", "efKsUSzoRFn9ZBaZ/RtyNU3cDKLNvwk1t60IXkGUp+4=", "gZ/8RwtNXnbHy0XQVQvMNWbz3dH/CZoaD5psDmqbqfA=", "Ez9Gz/HhPsclWpSa0Fgb/vzzJy0pG2qipsiVdKIXTP0=", "VVVTnRmuBCxwOQQjgfuLpIazaZcYuLhTS+ikYJCsCls=", "4CH9DtwU2zYrZ9qUZl3uAe9OxRu2x5noQilFi7lzLsA=", "V3SHBeo1SJeslxL3bL4ggmkG+Ae7JRndZn4pbXwhW1I=", "B41cz2CSEJ9SzZtPKmTt3aoFimFHB+TgKBZlpA51tRo=", "hD+3WUEXjZ6WT3WXlSRFe4qTS57AggHXyNl5871avXA=", "tW3v06WJMV0TlSVp/Fec5uo48Y9orbDMzTgvZ3rfadU=", "Q+S8f3tsZ+06GTu3WNJEU5NmlBxL5zCvf6SY7wsprUA=", "8FypyBxrno3aKnCS7J23iPzgnLBSvjUkg8eg+chr1M0=", "6pCT6g5GHRmEQS3R59wXfr7x50sF4TJE2kUgep9QU3I=", "73kiebcSaBo2RCXLdboD3+6N61i6Gj25lkXialUfvxU=", "BeTqXpHlFNq1CngKWpxLgQTkKwyj/lxgwYtGMd8+Pwc=", "+HtbS1y8HDXHDQ7r5wh4Byx3UWwWSDLTFAOKigQ6a8Y=", "wpkbbq+0GyngA5IJKdzetumYiTvuOmnKGgfUmlFdjVk=", "ByeAcK8L2lSzCcjsEUFi2yqNizg34RzzEuWBTkv05F4=", "KLqqNTciEAGjnFPUxXDiIVb9u1XhdhArjJ4qrDHTzZw=", "al8ij991qFLzGIRRzFs2PJb+2T0LQyZsf4mWY2RhhqE=", "j8Q5WixVLmZbW2IfFUQ2wc3ocnRuc/cApZ+hL0JSajo=", "Fi7UckOVlOJXFw+B0AUgNZ5Wl//upnNKyoXeGczabAw=", "IPiSl5h3aP6tfTi43zlgIitA+id0YiE9CEBxU9d4DCM=", "r/F253EH3ddgkUoRvELRBEYZmx5fHQUgLU5oyutrDVE=", "pQB7tf/8b66dhkapuDUFQcHkGUMbeMeYBtuEVBeMd/M=", "rwoLKXwO6ZXkoxbV02X6/Zr8cZeS/auZTADHBC16tzg=", "tTUr0+zuYhgtMqxBki027K3mxKOYqdnr9k3PtE3o5n0=", "0atSpnAsCDDb9ct92mU9xImf0pwD3micVNoI1yeW5gE=", "z+E8jByOckcQeoyl/qLx/Vz7JCL3iSBduWPydP6eVdc=", "R1Rm4dUh9M2geB0sX1MmRkheDM0f57oYIeiE9jz5Gis=", "DyWkAAz5cr1lm/KidFxhgp458naVxS74cwCxtL+L628=", "HM5Yluibg5rrqp/V/rDHJEL4tNVbtnfWBhv1T5OYNwA=", "jSiOi5XllYIUX11t9LGDtfl4zeADjFKEd/iWIZqur9Q=", "xUg6OTBKjJhcyoZp5ZRigUv2ifmRhIEduUgsebzvOMA=", "tx+EQRGLbRDPdP8Cd7drm8MJf5kHUtjIoQibwUyBLRo=", "klmFLxeid6a4pLP5vhTCipT7Kq6W28GUTzj8xi3ZTPM=", "fL06TDYuIF5i0QFFnkAIUZKgaiLhe2ucnig892yJVD0=", "k2XMoZOp1ZhInaZRT57WNSOonpz7SezoPk/WwutAMZE=", "7dP5qaCPo/OyjcA6DG+7ejF78ddZ1+9MB69LRlvloaQ=", "O6LIyo2lyRqdmf+byhrUqbtStCWO8Bw0ja3AdZlj6Hw=", "fPBZu3p2QayjyGS/RA6DVz/Nrik33XzmkdUa0nG0R6c=", "MB45oBpKjL36mPXPod7HRZaiO9lvWIHESsNHh8PavjM=", "nTmnrj1kXbTaVcZv1/m6vjilo8Y7pHc8ELNItEfVHq8=", "vkDTw18JOulXZR5sC3nk6iJl2L3brlToX89nLwTPQe8=", "2QXFV2LSt5tL5yxOMrp7Kv+tl0YwK1A1ly4EgjyF2z4=", "cTsKzQd4FwplPbVO4yL8lhNrDL3dUWvYQYA3JH0+gtU=", "tngY2VsqEz/RnM5jIxzrQdIJifl+DFAuhyCt491tuFQ=", "jcliRbwePp/r/x3Esa9VO/s8VBUnaWEvd1NvL15X8as=", "KQr3t3v6R25+Vv/4gq9D7aAFh8l4lbPJgohW6kRJUmo=", "Sv5+uwszN/0JFD8hJm4XgPWDgktnI8O0rOfdUD+m2UA=", "+kqzuThIs2E+o2NIWKDzClm8l/stKWntrsljUo+A9PQ=", "WiSXUeduxPmreDMwlni5I+VNvOsFahg1mzLJwZmSaEs=", "vbkElSvhSpdtEylhwt/wPo2DmklO4zQ7ncYtIkhO7WY=", "UuR91DmQiqCpztKhVjxCokJq+QHt7R42ea6wAAffVc8=", "rNa7AvsiK/eNy5VdsMHIZID9WlERJsX+SL9Ew+S5PUI=", "kKtXpnEpLtIvI+GpRR/rY1wkzoWW7Tr9LjlR+S8j+Cg=", "6edb6srLVS4M/r5GEp+gJsrkqkErVJs1SeCVFV/5X+U=", "X/M9+LgK80TGgLjCJkiSRr+7N0bx9+l/awAaQ9JsjHI=", "KnddTCkWdbCJYG12i+SlCRc+gW46oBjzNBCej2kS7sg=", "saCUC2AyERueiYZYgdwSTmrtUac24k0bQlzwLv2Znh4=", "I+MDQmGgn6nVyRdIMqQpHqDqTbk+zDKCUot6PhdSguw=", "opd07Ix+/ENWw27af04RPzByqSeTVMJIwlAlsh9MQU4=", "sZKfGuRHxlf02k8s0ysEu7VsYoq2eTpFqOQ2WVYHlIo=", "4x7g20+P1CM2GeSws890zC8OAy5z856Bh7ilbKC/5+U=", "iiXxuVQIQBvsMBpdIbkBHEoXVvBY+467fZdtMyUxEBo=", "X33D0dd6vHrEY4w7BgsUIx9NoYfszECIvkVK/HlC4CE=", "zHlTR98i/wKEfhf0smQli5nhslJLw8P5KjaZibqE1/Y=", "pit6TKuZ4deek4u2XEl1y6dv9SK5Wltga8H84hk/2QE=", "C1cm0fszdCifcesj2cSWbKxlM8tRZ8yln6tP/RTbU10=", "ap6Iz7bdGJdSt3xFv8oe2u3OzR+aCCBjYAQDEynrcZU=", "EWammco9jrCSMSmw1LRDttDXm17laQkMTpHQPsz6YVs=", "jlfaImsCJFQ6Nlw7j3nmDoWB2j0PoLPOATkRh2XAaWs=", "bI6yWpHLTdMI7Q2S2KWkkKHFWaVXCOogxFJabfd6NPc=", "/qz2iaVU1ProbjOdggUQ9MwAzWHtQ3lyYo7FCrfCkd4=", "SrMQSW043nU/K07vV9NqV+w0VGNT031QCWWxKLO1Tkk=", "2aXUTLSS9vVoOZOOzkR4Vis+hNnwPQTyyq/oXN/Pd3g=", "k5XoQxIN6TRCVlh0huhtle5Xts9WxYMFGV5VtPwPP5Q=", "UlPo+q+ZcYW1nytNXXD8WnyDQnVp4oYTEkVbdQJD5kQ=", "CCh1Jowq+uAzIQwwAPvhZ4qUIIImwrw5M/lkXdnxFO8=", "Olj/KDPUfIA6c4TqdoDUl4G5K9QY051/UYmz3x71VrE=", "8dUvjeaIxhgw49XdY4PkYuZWVEyBLWOCcKVkBZEYq3g=", "VqbAuB4OCgNwSH6BbYmdxrV/YrQpnF0e/Br+od1dAZE=", "X1u0MgLnC40Uitl0dxnVVf7N6fiOHzxJHK8UdRf2OoM=", "0pHqLAA0T1VzhNdiTfb6sgia6SaQ7uXN6Eosal1x5hE=", "23g7LJgMLogyefYG92haKIAsEsoE5vDyAUbG17dr9bo=", "Tcav7648hOCTXadXpFRdDW8S+AYo0TQ5UHFIgtR6kag=", "P3jD4h7bPTNpEPpqsHhsVSewV70AhtdqbdCQ4UMjVTU=", "cr85hUoXacBk3FnS9C2be8Ld9NEUcQ9EmDkyVE9qFmI=", "Wma/eK2p/7v88+hh0OCIPrY0uQUauO9M18QD8Kkm2g0=", "r1BqVcTXdYNa08u8zjZSLfOORR4MUYc2VFRT5xTg83U=", "v876ilzsCe0DlwSsp3hO6It6+Z9CCw7qjMv948Q4VRU=", "sUtD5fXLMerUeY3Sqpv7PCnC15x3RD2QttoD5VX7TaI=", "M5CJdq1qykeX8Qf6DRpqSrn+Til57z9VQjsrot/B/SI=", "oAmr2HG7Lj4RbADoDP/j2ZVzMW00fHcSRXSh9SBjhuk=", "jHKuO9XHSyqIyqcVnQasF0ZJKuBrYqHSAqNcBbsNLxk=", "i/x1Fr1NWie3SsnRCX9ShsABZX1wDMW0kprOj8x5upo=", "qXp8/EgiYV01YaEAGRMRDdwNQyPWFCTT5sOWM6+bDY8=", "RH+81xNrReRtDVpNoSNzaypF/IDXu0BLnUByLO/YogY=", "DOfD0TSmt4B/f/20AjYBebzQN/QbCGxxYnMroHSATJc=", "NzduHTaEwVrAdTz2QVcmewc1Ea3zx4PqEQV27nXXZQg=", "SEca/PoK8Gz/bKZxotURqbbpzsZav9gVOnYB0/BstIU=", "2bTaLWb0i8r+S5Lp3uBhjKAcUwjOU0cVuKSRNM7Zlb4=", "y2FdqPfaAzNvEu9yIkFMYtXGEOv3DjLXENSo9LsKtUQ=", "ulnNInjNVUBStGToqiwNcCPJ4g3AGRgEeFYrAPRamOM=", "kt17RNzf7dnY10FEPpbE+/lWNMF5MTqHGrRF/px9WXU=", "lVlzgdBxmO1HTKJj+gtlv9gjpJYdUdnpsryW9oxpyjk=", "Ja3+h2IcnXeZmPaOZ8srM/BuadIfA+LCuCsfaGlyeJ8=", "vTHU6RXHY/CJc3PVxx0VDTlFk45Tp3x33w785K9sF4Q=", "DHuxL8UwO7pxNRpFdMigYNsPR3f1IqNiplircAmVZng=", "46IlMdYNZuV4iFy4Bb+nQtRbBk37ZVH7Td70Tjd/9Mc=", "i1j45k7CydWxQ0ZAfMN1cYAwJRmZG+7jw9RBqwqvbbU=", "dyLq2x5faNTU7F/Nl64ptbtwJZIZZWQa2CayTotWMJQ=", "zgWIg83qpRKEx87lsHlTenEpLUMUp07+iz1rWINim88=", "S2gRzj+lwQykBlyVdl4sA179L7PYWvn/8gj8nHmjhh0=", "vtQxRuEGyV5LJt1/RSBqNazubRsdOdCOa1UAmKlODDI=", "C1qtjgn83z/wZfR6Inr2gN977uY1Gp8m6OcH3Fp/NX8=", "Q3jyxDIlzEwQx3WB6EY14GPdJTNiJCmK4Eh08GcIKqw=", "CKxlB6hth2M76ZE1ijgNI3aDt/BAhZeOW8Hx449SfDg=", "ycc3sRfjCN7WCbkcgyKJB55RrbK4h9W4YZRBcMJYM3g=", "H61ZteP3Eh1bc85vW4TSkeiGTB5OWXe+nwKD5DQqUGw=", "XX1xk0pWwH5Vd5BFWLkG+iTl+a258om+vB3uEAkEu8I=", "NQCuMnSIKPxqc8SC2GRyiYe6/EiWEOn/wZSF4POF0wk=", "2A+JXA5ICMpvwMb4TYGdMax917cRyqGjx3tn2/l9O3Q=", "7JZu3Vtjyh2NvUTgQcjk3/77cVyh7oLfRQZgkxDGVdo="], "CachedAssets": {"+AxQixWoYhyv1KwXECDvPNQgpVNWAhhc5ABlAKjZ3ZY=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\ofa40bqe71-b9sayid5wm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "css/site.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0suozzrugb", "Integrity": "0FC1RbVkLkwKhJ1oi+r2Hw4Tm9av6DyJeo95YVvfA3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\css\\site.css", "FileLength": 318, "LastWriteTime": "2025-07-16T18:56:39.8728405+00:00"}, "QXFjzqvrnAAtIfJ27dK4+/HXnpZMUXljiKQpBXx0mcg=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\j9swiz3yzq-90yqlj465b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "favicon.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fxo9vb9gbf", "Integrity": "cwHpDnBTabxDzOHXFPOawb7ZlL3eysr4s1HfAD/K3vA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\favicon.ico", "FileLength": 9541, "LastWriteTime": "2025-07-16T18:56:39.8762135+00:00"}, "B9OFDgoGnyvI9vEcnA5z5g4XMbdHZaBcv0r+qks1vtI=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\vltxs1u3vm-xtxxf3hu2r.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "js/site.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-07-16T18:56:39.8881622+00:00"}, "efKsUSzoRFn9ZBaZ/RtyNU3cDKLNvwk1t60IXkGUp+4=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\14ipv7q33s-bqjiyaj88i.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-16T18:56:39.9208834+00:00"}, "gZ/8RwtNXnbHy0XQVQvMNWbz3dH/CZoaD5psDmqbqfA=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\rh65p086id-c2jlpeoesf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-16T18:56:39.9318689+00:00"}, "Ez9Gz/HhPsclWpSa0Fgb/vzzJy0pG2qipsiVdKIXTP0=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\eb27mqwgz2-erw9l3u2r3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-16T18:56:39.9457211+00:00"}, "VVVTnRmuBCxwOQQjgfuLpIazaZcYuLhTS+ikYJCsCls=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\9glsckmvxo-aexeepp0ev.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-16T18:56:39.9681791+00:00"}, "4CH9DtwU2zYrZ9qUZl3uAe9OxRu2x5noQilFi7lzLsA=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\hh8ihyobdx-d7shbmvgxk.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-16T18:56:39.9427058+00:00"}, "V3SHBeo1SJeslxL3bL4ggmkG+Ae7JRndZn4pbXwhW1I=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\bww8ud00yd-ausgxo2sd3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-16T18:56:39.9457211+00:00"}, "B41cz2CSEJ9SzZtPKmTt3aoFimFHB+TgKBZlpA51tRo=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\edcrak57d7-k8d9w2qqmf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-16T18:56:39.9479642+00:00"}, "hD+3WUEXjZ6WT3WXlSRFe4qTS57AggHXyNl5871avXA=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\sf8kf2lula-cosvhxvwiu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-16T18:56:39.8752066+00:00"}, "tW3v06WJMV0TlSVp/Fec5uo48Y9orbDMzTgvZ3rfadU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\3tzsqhslk9-ub07r2b239.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-16T18:56:39.8892714+00:00"}, "Q+S8f3tsZ+06GTu3WNJEU5NmlBxL5zCvf6SY7wsprUA=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\oxk5o6czdw-fvhpjtyr6v.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-16T18:56:39.9168796+00:00"}, "8FypyBxrno3aKnCS7J23iPzgnLBSvjUkg8eg+chr1M0=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\k8b25g0zwc-b7pk76d08c.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-16T18:56:39.9208834+00:00"}, "6pCT6g5GHRmEQS3R59wXfr7x50sF4TJE2kUgep9QU3I=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\8axix7wldr-fsbi9cje9m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-16T18:56:39.9283612+00:00"}, "73kiebcSaBo2RCXLdboD3+6N61i6Gj25lkXialUfvxU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\d5z9sfpxbi-rzd6atqjts.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-16T18:56:39.9527298+00:00"}, "BeTqXpHlFNq1CngKWpxLgQTkKwyj/lxgwYtGMd8+Pwc=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\u8428c4e9i-ee0r1s7dh0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-16T18:56:39.9567422+00:00"}, "+HtbS1y8HDXHDQ7r5wh4Byx3UWwWSDLTFAOKigQ6a8Y=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\tg53r17ghy-dxx9fxp4il.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-16T18:56:39.948966+00:00"}, "wpkbbq+0GyngA5IJKdzetumYiTvuOmnKGgfUmlFdjVk=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\nlrl0f3xs3-jd9uben2k1.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-16T18:56:39.9681791+00:00"}, "ByeAcK8L2lSzCcjsEUFi2yqNizg34RzzEuWBTkv05F4=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\1fc4q00scq-khv3u5hwcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-16T18:56:39.9736981+00:00"}, "KLqqNTciEAGjnFPUxXDiIVb9u1XhdhArjJ4qrDHTzZw=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\8xykfy6qmd-r4e9w2rdcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-16T18:56:39.8782134+00:00"}, "al8ij991qFLzGIRRzFs2PJb+2T0LQyZsf4mWY2RhhqE=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\vsrbd71spn-lcd1t2u6c8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-16T18:56:39.8881622+00:00"}, "j8Q5WixVLmZbW2IfFUQ2wc3ocnRuc/cApZ+hL0JSajo=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\cynp17w084-c2oey78nd0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-16T18:56:39.8984953+00:00"}, "Fi7UckOVlOJXFw+B0AUgNZ5Wl//upnNKyoXeGczabAw=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\7o8oyvng68-tdbxkamptv.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-16T18:56:39.9168796+00:00"}, "IPiSl5h3aP6tfTi43zlgIitA+id0YiE9CEBxU9d4DCM=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\h9vwtoirpy-j5mq2jizvt.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-16T18:56:39.9293673+00:00"}, "r/F253EH3ddgkUoRvELRBEYZmx5fHQUgLU5oyutrDVE=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\o5k23vqhrk-06098lyss8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-16T18:56:39.9412138+00:00"}, "pQB7tf/8b66dhkapuDUFQcHkGUMbeMeYBtuEVBeMd/M=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\iznc766avz-nvvlpmu67g.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-16T18:56:39.9437128+00:00"}, "rwoLKXwO6ZXkoxbV02X6/Zr8cZeS/auZTADHBC16tzg=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\ns4583i71s-s35ty4nyc5.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-16T18:56:39.948966+00:00"}, "tTUr0+zuYhgtMqxBki027K3mxKOYqdnr9k3PtE3o5n0=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\47012z8l2l-pj5nd1wqec.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-16T18:56:39.9607421+00:00"}, "0atSpnAsCDDb9ct92mU9xImf0pwD3micVNoI1yeW5gE=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\4vzefxwp2m-46ein0sx1k.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-16T18:56:39.9933189+00:00"}, "z+E8jByOckcQeoyl/qLx/Vz7JCL3iSBduWPydP6eVdc=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\bh4v3mdph9-v0zj4ognzu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-16T18:56:39.8881622+00:00"}, "R1Rm4dUh9M2geB0sX1MmRkheDM0f57oYIeiE9jz5Gis=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\l186vlgaag-37tfw0ft22.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-16T18:56:39.9248445+00:00"}, "DyWkAAz5cr1lm/KidFxhgp458naVxS74cwCxtL+L628=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\rsc7qacj1u-hrwsygsryq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-16T18:56:39.9522236+00:00"}, "HM5Yluibg5rrqp/V/rDHJEL4tNVbtnfWBhv1T5OYNwA=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\z408qb6q7a-pk9g2wxc8p.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-16T18:56:39.9557412+00:00"}, "jSiOi5XllYIUX11t9LGDtfl4zeADjFKEd/iWIZqur9Q=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\qfr28sqcy1-ft3s53vfgj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-16T18:56:39.9817042+00:00"}, "xUg6OTBKjJhcyoZp5ZRigUv2ifmRhIEduUgsebzvOMA=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\0k1i85ntyh-6cfz1n2cew.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-16T18:56:39.9875818+00:00"}, "tx+EQRGLbRDPdP8Cd7drm8MJf5kHUtjIoQibwUyBLRo=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\xa379ftczi-6pdc2jztkx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-16T18:56:40.0033538+00:00"}, "klmFLxeid6a4pLP5vhTCipT7Kq6W28GUTzj8xi3ZTPM=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\lr5hmrr0j7-493y06b0oq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-16T18:56:39.9557412+00:00"}, "fL06TDYuIF5i0QFFnkAIUZKgaiLhe2ucnig892yJVD0=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\ivy2s9gwh5-iovd86k7lj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-16T18:56:39.963247+00:00"}, "k2XMoZOp1ZhInaZRT57WNSOonpz7SezoPk/WwutAMZE=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\higufxz8op-vr1egmr9el.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-16T18:56:39.9746985+00:00"}, "7dP5qaCPo/OyjcA6DG+7ejF78ddZ1+9MB69LRlvloaQ=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\s3tglgz8hr-kbrnm935zg.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-16T18:56:39.8892714+00:00"}, "O6LIyo2lyRqdmf+byhrUqbtStCWO8Bw0ja3AdZlj6Hw=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\vw9sls9bhj-jj8uyg4cgr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-16T18:56:39.8974934+00:00"}, "fPBZu3p2QayjyGS/RA6DVz/Nrik33XzmkdUa0nG0R6c=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\ym8tkpcl2i-y7v9cxd14o.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-16T18:56:39.9168796+00:00"}, "MB45oBpKjL36mPXPod7HRZaiO9lvWIHESsNHh8PavjM=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\zzna4nrm5w-notf2xhcfb.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-16T18:56:39.9293673+00:00"}, "nTmnrj1kXbTaVcZv1/m6vjilo8Y7pHc8ELNItEfVHq8=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\igscs4x0yk-h1s4sie4z3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-16T18:56:39.948966+00:00"}, "vkDTw18JOulXZR5sC3nk6iJl2L3brlToX89nLwTPQe8=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\zap00c0tb5-63fj8s7r0e.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-16T18:56:39.9503554+00:00"}, "2QXFV2LSt5tL5yxOMrp7Kv+tl0YwK1A1ly4EgjyF2z4=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\pogzkicjpz-0j3bgjxly4.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-16T18:56:39.9557412+00:00"}, "cTsKzQd4FwplPbVO4yL8lhNrDL3dUWvYQYA3JH0+gtU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\j3hwsq15kh-47otxtyo56.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-07-16T18:56:39.9587444+00:00"}, "tngY2VsqEz/RnM5jIxzrQdIJifl+DFAuhyCt491tuFQ=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\q3naettu8c-4v8eqarkd7.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-07-16T18:56:39.9607421+00:00"}, "jcliRbwePp/r/x3Esa9VO/s8VBUnaWEvd1NvL15X8as=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\ldxy2n3w6q-356vix0kms.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-07-16T18:56:39.9627418+00:00"}, "KQr3t3v6R25+Vv/4gq9D7aAFh8l4lbPJgohW6kRJUmo=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\ypthv7q62w-83jwlth58m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-07-16T18:56:39.8752066+00:00"}, "Sv5+uwszN/0JFD8hJm4XgPWDgktnI8O0rOfdUD+m2UA=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\t6e3b82hrf-mrlpezrjn3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-07-16T18:56:39.8847345+00:00"}, "+kqzuThIs2E+o2NIWKDzClm8l/stKWntrsljUo+A9PQ=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\uocis9wxbx-lzl9nlhx6b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-07-16T18:56:39.900495+00:00"}, "WiSXUeduxPmreDMwlni5I+VNvOsFahg1mzLJwZmSaEs=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\2tikzqlmtu-ag7o75518u.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-07-16T18:56:39.9158834+00:00"}, "vbkElSvhSpdtEylhwt/wPo2DmklO4zQ7ncYtIkhO7WY=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\9muusbdg0a-x0q3zqp4vz.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/LICENSE.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-07-16T18:56:39.9258526+00:00"}, "UuR91DmQiqCpztKhVjxCokJq+QHt7R42ea6wAAffVc8=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\tpsq5dibur-0i3buxo5is.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-07-16T18:56:39.933887+00:00"}, "rNa7AvsiK/eNy5VdsMHIZID9WlERJsX+SL9Ew+S5PUI=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\t0qo7o574p-o1o13a6vjx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-07-16T18:56:39.9597424+00:00"}, "kKtXpnEpLtIvI+GpRR/rY1wkzoWW7Tr9LjlR+S8j+Cg=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\b9lhb08218-ttgo8qnofa.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-07-16T18:56:39.9617418+00:00"}, "6edb6srLVS4M/r5GEp+gJsrkqkErVJs1SeCVFV/5X+U=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\a3ygkwa5zy-2z0ns9nrw6.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-07-16T18:56:39.9671718+00:00"}, "X/M9+LgK80TGgLjCJkiSRr+7N0bx9+l/awAaQ9JsjHI=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\nezrqnk58p-muycvpuwrr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-07-16T18:56:39.9711811+00:00"}, "KnddTCkWdbCJYG12i+SlCRc+gW46oBjzNBCej2kS7sg=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\v0zgfp0y55-87fc7y1x7t.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-07-16T18:56:39.8782134+00:00"}, "saCUC2AyERueiYZYgdwSTmrtUac24k0bQlzwLv2Znh4=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\yfc7ypekbg-mlv21k5csn.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-07-16T18:56:39.8827252+00:00"}, "NQCuMnSIKPxqc8SC2GRyiYe6/EiWEOn/wZSF4POF0wk=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\rot3sofme1-mlv21k5csn.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-07-16T18:56:39.939215+00:00"}, "I+MDQmGgn6nVyRdIMqQpHqDqTbk+zDKCUot6PhdSguw=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\lprs8bz5z8-sh82cr0ebb.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "css/site#[.{fingerprint=sh82cr0ebb}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rcujs1u80a", "Integrity": "lb+7JeK4Wv7wIEjWlJMKAfWf0K/1k6PU4NOWPrx+MVg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\css\\site.css", "FileLength": 2353, "LastWriteTime": "2025-07-16T18:56:39.900495+00:00"}, "opd07Ix+/ENWw27af04RPzByqSeTVMJIwlAlsh9MQU4=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\yyut3rtmm3-61n19gt1b8.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-07-16T18:56:39.9148806+00:00"}, "sZKfGuRHxlf02k8s0ysEu7VsYoq2eTpFqOQ2WVYHlIo=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\d2s9mt8l3v-xtxxf3hu2r.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "js/site#[.{fingerprint=xtxxf3hu2r}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-07-16T18:56:39.9188808+00:00"}, "4x7g20+P1CM2GeSws890zC8OAy5z856Bh7ilbKC/5+U=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\o4jn2nrnp2-bqjiyaj88i.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-16T18:56:39.9226756+00:00"}, "iiXxuVQIQBvsMBpdIbkBHEoXVvBY+467fZdtMyUxEBo=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\zn20i0tc9m-c2jlpeoesf.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-16T18:56:39.963247+00:00"}, "X33D0dd6vHrEY4w7BgsUIx9NoYfszECIvkVK/HlC4CE=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\xh28ms43oh-erw9l3u2r3.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-16T18:56:39.9617418+00:00"}, "zHlTR98i/wKEfhf0smQli5nhslJLw8P5KjaZibqE1/Y=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\bhug7or2m3-aexeepp0ev.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-16T18:56:39.963247+00:00"}, "pit6TKuZ4deek4u2XEl1y6dv9SK5Wltga8H84hk/2QE=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\ri3kjc12fg-d7shbmvgxk.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-16T18:56:39.963247+00:00"}, "C1cm0fszdCifcesj2cSWbKxlM8tRZ8yln6tP/RTbU10=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\nlb4e9rxe4-ausgxo2sd3.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-16T18:56:39.8752066+00:00"}, "ap6Iz7bdGJdSt3xFv8oe2u3OzR+aCCBjYAQDEynrcZU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\q8c3herb64-k8d9w2qqmf.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-16T18:56:39.8812157+00:00"}, "EWammco9jrCSMSmw1LRDttDXm17laQkMTpHQPsz6YVs=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\920u11wdzr-cosvhxvwiu.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-16T18:56:39.8917615+00:00"}, "jlfaImsCJFQ6Nlw7j3nmDoWB2j0PoLPOATkRh2XAaWs=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\xo9ezg8h62-ub07r2b239.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-16T18:56:39.9148806+00:00"}, "bI6yWpHLTdMI7Q2S2KWkkKHFWaVXCOogxFJabfd6NPc=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\mhqzf0e8x2-fvhpjtyr6v.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-16T18:56:39.9178802+00:00"}, "/qz2iaVU1ProbjOdggUQ9MwAzWHtQ3lyYo7FCrfCkd4=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\0j4di07hif-b7pk76d08c.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-16T18:56:39.9188808+00:00"}, "SrMQSW043nU/K07vV9NqV+w0VGNT031QCWWxKLO1Tkk=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\bk6g14ginu-fsbi9cje9m.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-16T18:56:39.9258526+00:00"}, "2aXUTLSS9vVoOZOOzkR4Vis+hNnwPQTyyq/oXN/Pd3g=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\q6yglsnvlc-rzd6atqjts.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-16T18:56:39.9268549+00:00"}, "k5XoQxIN6TRCVlh0huhtle5Xts9WxYMFGV5VtPwPP5Q=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\aqwvpajyd3-ee0r1s7dh0.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-16T18:56:39.9293673+00:00"}, "UlPo+q+ZcYW1nytNXXD8WnyDQnVp4oYTEkVbdQJD5kQ=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\tlhotw0jnm-dxx9fxp4il.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-16T18:56:39.965701+00:00"}, "CCh1Jowq+uAzIQwwAPvhZ4qUIIImwrw5M/lkXdnxFO8=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\kkurdxpm9f-jd9uben2k1.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-16T18:56:39.8718327+00:00"}, "Olj/KDPUfIA6c4TqdoDUl4G5K9QY051/UYmz3x71VrE=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\yhfvs28ewf-khv3u5hwcm.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-16T18:56:39.8752066+00:00"}, "8dUvjeaIxhgw49XdY4PkYuZWVEyBLWOCcKVkBZEYq3g=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\f1ac5fdwia-r4e9w2rdcm.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-16T18:56:39.8847345+00:00"}, "VqbAuB4OCgNwSH6BbYmdxrV/YrQpnF0e/Br+od1dAZE=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\jhq81lqatb-lcd1t2u6c8.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-16T18:56:39.9138741+00:00"}, "X1u0MgLnC40Uitl0dxnVVf7N6fiOHzxJHK8UdRf2OoM=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\oiqa6mlryd-c2oey78nd0.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-16T18:56:39.9188808+00:00"}, "0pHqLAA0T1VzhNdiTfb6sgia6SaQ7uXN6Eosal1x5hE=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\5c5phhd1cg-tdbxkamptv.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-16T18:56:39.9226756+00:00"}, "23g7LJgMLogyefYG92haKIAsEsoE5vDyAUbG17dr9bo=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\9tv9vhb97z-j5mq2jizvt.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-16T18:56:39.9427058+00:00"}, "Tcav7648hOCTXadXpFRdDW8S+AYo0TQ5UHFIgtR6kag=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\dsz1cg7n1f-06098lyss8.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-16T18:56:39.9447123+00:00"}, "P3jD4h7bPTNpEPpqsHhsVSewV70AhtdqbdCQ4UMjVTU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\80l2hrri5r-nvvlpmu67g.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-16T18:56:39.9469572+00:00"}, "cr85hUoXacBk3FnS9C2be8Ld9NEUcQ9EmDkyVE9qFmI=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\hurbbjqq0l-s35ty4nyc5.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-16T18:56:39.9503554+00:00"}, "Wma/eK2p/7v88+hh0OCIPrY0uQUauO9M18QD8Kkm2g0=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\nocwlel4l8-pj5nd1wqec.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-16T18:56:39.8861513+00:00"}, "r1BqVcTXdYNa08u8zjZSLfOORR4MUYc2VFRT5xTg83U=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\2o1knfnnfx-46ein0sx1k.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-16T18:56:39.8984953+00:00"}, "v876ilzsCe0DlwSsp3hO6It6+Z9CCw7qjMv948Q4VRU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\s2f91uc2id-v0zj4ognzu.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-16T18:56:39.9303695+00:00"}, "sUtD5fXLMerUeY3Sqpv7PCnC15x3RD2QttoD5VX7TaI=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\6t5pwr2opk-37tfw0ft22.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-16T18:56:39.9469572+00:00"}, "M5CJdq1qykeX8Qf6DRpqSrn+Til57z9VQjsrot/B/SI=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\zjnmpfyr4b-hrwsygsryq.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-16T18:56:39.9607421+00:00"}, "oAmr2HG7Lj4RbADoDP/j2ZVzMW00fHcSRXSh9SBjhuk=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\d9lo8u5fbp-pk9g2wxc8p.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-16T18:56:39.9787047+00:00"}, "jHKuO9XHSyqIyqcVnQasF0ZJKuBrYqHSAqNcBbsNLxk=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\rd59fx66rg-ft3s53vfgj.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-16T18:56:39.9978487+00:00"}, "i/x1Fr1NWie3SsnRCX9ShsABZX1wDMW0kprOj8x5upo=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\1kvxxii4kz-6cfz1n2cew.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-16T18:56:39.9547355+00:00"}, "qXp8/EgiYV01YaEAGRMRDdwNQyPWFCTT5sOWM6+bDY8=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\nlj8cqdux7-6pdc2jztkx.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-16T18:56:39.9862219+00:00"}, "RH+81xNrReRtDVpNoSNzaypF/IDXu0BLnUByLO/YogY=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\y5kr9kwq5m-493y06b0oq.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-16T18:56:39.9888034+00:00"}, "DOfD0TSmt4B/f/20AjYBebzQN/QbCGxxYnMroHSATJc=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\w8uh90b10i-iovd86k7lj.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-16T18:56:39.8812157+00:00"}, "NzduHTaEwVrAdTz2QVcmewc1Ea3zx4PqEQV27nXXZQg=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\2ruhixa8xd-vr1egmr9el.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-16T18:56:39.8881622+00:00"}, "SEca/PoK8Gz/bKZxotURqbbpzsZav9gVOnYB0/BstIU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\foy5i872gl-kbrnm935zg.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-16T18:56:39.9198812+00:00"}, "2bTaLWb0i8r+S5Lp3uBhjKAcUwjOU0cVuKSRNM7Zlb4=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\2z1qdpnfqu-jj8uyg4cgr.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-16T18:56:39.9283612+00:00"}, "y2FdqPfaAzNvEu9yIkFMYtXGEOv3DjLXENSo9LsKtUQ=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\muzxgjo37n-y7v9cxd14o.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-16T18:56:39.9557412+00:00"}, "ulnNInjNVUBStGToqiwNcCPJ4g3AGRgEeFYrAPRamOM=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\lpjzqdhimc-notf2xhcfb.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-16T18:56:39.9597424+00:00"}, "kt17RNzf7dnY10FEPpbE+/lWNMF5MTqHGrRF/px9WXU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\z8ri5bwbu2-h1s4sie4z3.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-16T18:56:39.9671718+00:00"}, "lVlzgdBxmO1HTKJj+gtlv9gjpJYdUdnpsryW9oxpyjk=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\fhw1z6t0qj-63fj8s7r0e.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-16T18:56:39.963247+00:00"}, "Ja3+h2IcnXeZmPaOZ8srM/BuadIfA+LCuCsfaGlyeJ8=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\stamlplyst-0j3bgjxly4.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-16T18:56:39.9681791+00:00"}, "vTHU6RXHY/CJc3PVxx0VDTlFk45Tp3x33w785K9sF4Q=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\5ub76y8isd-47otxtyo56.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-07-16T18:56:39.9691837+00:00"}, "DHuxL8UwO7pxNRpFdMigYNsPR3f1IqNiplircAmVZng=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\uz19c4uzko-4v8eqarkd7.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-07-16T18:56:39.8718327+00:00"}, "46IlMdYNZuV4iFy4Bb+nQtRbBk37ZVH7Td70Tjd/9Mc=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\3eodnqfdjc-356vix0kms.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-07-16T18:56:39.8718327+00:00"}, "i1j45k7CydWxQ0ZAfMN1cYAwJRmZG+7jw9RBqwqvbbU=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\jh9e11aaux-83jwlth58m.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-07-16T18:56:39.8752066+00:00"}, "dyLq2x5faNTU7F/Nl64ptbtwJZIZZWQa2CayTotWMJQ=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\m0lf1s7rkh-mrlpezrjn3.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-07-16T18:56:39.8812157+00:00"}, "zgWIg83qpRKEx87lsHlTenEpLUMUp07+iz1rWINim88=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\6j2252c6ih-lzl9nlhx6b.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-07-16T18:56:39.8892714+00:00"}, "S2gRzj+lwQykBlyVdl4sA179L7PYWvn/8gj8nHmjhh0=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\za5cs44o7u-ag7o75518u.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-07-16T18:56:39.8994947+00:00"}, "vtQxRuEGyV5LJt1/RSBqNazubRsdOdCOa1UAmKlODDI=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\5u6nkyp10k-x0q3zqp4vz.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-07-16T18:56:39.9168796+00:00"}, "C1qtjgn83z/wZfR6Inr2gN977uY1Gp8m6OcH3Fp/NX8=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\yw253t2yss-0i3buxo5is.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-07-16T18:56:39.9283612+00:00"}, "Q3jyxDIlzEwQx3WB6EY14GPdJTNiJCmK4Eh08GcIKqw=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\6id4mbo8qd-o1o13a6vjx.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-07-16T18:56:39.948966+00:00"}, "CKxlB6hth2M76ZE1ijgNI3aDt/BAhZeOW8Hx449SfDg=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\ajf14tfrko-ttgo8qnofa.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-07-16T18:56:39.9527298+00:00"}, "ycc3sRfjCN7WCbkcgyKJB55RrbK4h9W4YZRBcMJYM3g=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\zn8985znn9-2z0ns9nrw6.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-07-16T18:56:39.880215+00:00"}, "H61ZteP3Eh1bc85vW4TSkeiGTB5OWXe+nwKD5DQqUGw=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\aeg74zr7ou-muycvpuwrr.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-07-16T18:56:39.8974934+00:00"}, "XX1xk0pWwH5Vd5BFWLkG+iTl+a258om+vB3uEAkEu8I=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\l9n6f8vp69-87fc7y1x7t.gz", "SourceId": "LaptopShop", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-07-16T18:56:39.9148806+00:00"}, "2A+JXA5ICMpvwMb4TYGdMax917cRyqGjx3tn2/l9O3Q=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\5rw2t3q994-j3co77rom6.gz", "SourceId": "LaptopShop", "SourceType": "Computed", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "LaptopShop#[.{fingerprint=j3co77rom6}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\scopedcss\\bundle\\LaptopShop.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h5ngzj5kcv", "Integrity": "y6cIIr3wlNf0+FgVIjCktRedHi9IhpEyVL/6QqACTV8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\scopedcss\\bundle\\LaptopShop.styles.css", "FileLength": 539, "LastWriteTime": "2025-07-16T18:56:39.9402145+00:00"}, "7JZu3Vtjyh2NvUTgQcjk3/77cVyh7oLfRQZgkxDGVdo=": {"Identity": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\4tw343vpw4-j3co77rom6.gz", "SourceId": "LaptopShop", "SourceType": "Computed", "ContentRoot": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LaptopShop", "RelativePath": "LaptopShop#[.{fingerprint=j3co77rom6}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\LaptopShop.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h5ngzj5kcv", "Integrity": "y6cIIr3wlNf0+FgVIjCktRedHi9IhpEyVL/6QqACTV8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\LaptopShop\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\LaptopShop.bundle.scp.css", "FileLength": 539, "LastWriteTime": "2025-07-16T18:56:39.9402145+00:00"}}, "CachedCopyCandidates": {}}