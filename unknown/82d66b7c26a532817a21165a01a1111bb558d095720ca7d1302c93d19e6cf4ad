@model LaptopShop.Models.AdminLoginViewModel
@{
    ViewData["Title"] = "Đăng Nhập Admin";
    Layout = null;
}

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - LaptopShop Admin</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <link rel="stylesheet" href="~/css/site.css" />
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .admin-login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
        }
        .admin-logo {
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card admin-login-card shadow-lg">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <i class="fas fa-laptop fa-3x admin-logo mb-3"></i>
                            <h3 class="admin-logo fw-bold">LaptopShop Admin</h3>
                            <p class="text-muted">Đăng nhập vào hệ thống quản trị</p>
                        </div>

                        <form asp-action="Login" method="post">
                            <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                            
                            <div class="mb-3">
                                <label asp-for="Username" class="form-label fw-bold">Tên đăng nhập</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-user"></i>
                                    </span>
                                    <input asp-for="Username" class="form-control" placeholder="Nhập tên đăng nhập" />
                                </div>
                                <span asp-validation-for="Username" class="text-danger"></span>
                            </div>

                            <div class="mb-3">
                                <label asp-for="Password" class="form-label fw-bold">Mật khẩu</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <input asp-for="Password" class="form-control" placeholder="Nhập mật khẩu" />
                                </div>
                                <span asp-validation-for="Password" class="text-danger"></span>
                            </div>

                            <div class="mb-3 form-check">
                                <input asp-for="RememberMe" class="form-check-input" />
                                <label asp-for="RememberMe" class="form-check-label">
                                    Ghi nhớ đăng nhập
                                </label>
                            </div>

                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>Đăng Nhập
                                </button>
                            </div>
                        </form>

                        <div class="text-center">
                            <small class="text-muted">
                                <i class="fas fa-shield-alt me-1"></i>
                                Khu vực dành cho quản trị viên
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Demo Account Info -->
                <div class="card mt-3 admin-login-card">
                    <div class="card-body text-center">
                        <h6 class="text-muted mb-2">Tài khoản demo:</h6>
                        <p class="mb-1"><strong>Email:</strong> <EMAIL></p>
                        <p class="mb-0"><strong>Mật khẩu:</strong> Admin@123</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
