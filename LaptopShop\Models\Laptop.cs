using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LaptopShop.Models
{
    public class Laptop
    {
        public int Id { get; set; }
        
        [Required(ErrorMessage = "Tên laptop là bắt buộc")]
        [StringLength(200, ErrorMessage = "Tên laptop không được vượt quá 200 ký tự")]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(50)]
        public string? Model { get; set; }
        
        [Required(ErrorMessage = "Mô tả sản phẩm là bắt buộc")]
        [StringLength(2000)]
        public string Description { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Giá sản phẩm là bắt buộc")]
        [Range(0, double.MaxValue, ErrorMessage = "Giá phải lớn hơn hoặc bằng 0")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Price { get; set; }
        
        [Range(0, double.MaxValue, ErrorMessage = "Gi<PERSON> khuyến mãi phải lớn hơn hoặc bằng 0")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? SalePrice { get; set; }
        
        [Required(ErrorMessage = "Số lượng tồn kho là bắt buộc")]
        [Range(0, int.MaxValue, ErrorMessage = "Số lượng phải lớn hơn hoặc bằng 0")]
        public int StockQuantity { get; set; }
        
        public string? MainImageUrl { get; set; }
        
        public string? ImageUrls { get; set; } // JSON string chứa danh sách URL hình ảnh
        
        // Thông số kỹ thuật
        [StringLength(100)]
        public string? Processor { get; set; } // CPU
        
        [StringLength(50)]
        public string? RAM { get; set; } // RAM
        
        [StringLength(100)]
        public string? Storage { get; set; } // Ổ cứng
        
        [StringLength(100)]
        public string? GraphicsCard { get; set; } // Card đồ họa
        
        [StringLength(50)]
        public string? ScreenSize { get; set; } // Kích thước màn hình
        
        [StringLength(100)]
        public string? Resolution { get; set; } // Độ phân giải
        
        [StringLength(100)]
        public string? OperatingSystem { get; set; } // Hệ điều hành
        
        [StringLength(50)]
        public string? Weight { get; set; } // Trọng lượng
        
        [StringLength(100)]
        public string? BatteryLife { get; set; } // Thời lượng pin
        
        [StringLength(200)]
        public string? Connectivity { get; set; } // Kết nối (WiFi, Bluetooth, USB, etc.)
        
        [StringLength(100)]
        public string? Color { get; set; } // Màu sắc
        
        public bool IsFeatured { get; set; } = false;
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        public DateTime? UpdatedAt { get; set; }
        
        // Foreign Keys
        [Required(ErrorMessage = "Danh mục sản phẩm là bắt buộc")]
        public int CategoryId { get; set; }
        
        [Required(ErrorMessage = "Thương hiệu sản phẩm là bắt buộc")]
        public int BrandId { get; set; }
        
        // Navigation properties
        public virtual Category Category { get; set; } = null!;
        public virtual Brand Brand { get; set; } = null!;
        public virtual ICollection<OrderDetail> OrderDetails { get; set; } = new List<OrderDetail>();
        public virtual ICollection<Review> Reviews { get; set; } = new List<Review>();
        public virtual ICollection<CartItem> CartItems { get; set; } = new List<CartItem>();
    }
}
