using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using LaptopShop.Data;
using LaptopShop.Models;

namespace LaptopShop.Controllers
{
    public class ProductController : Controller
    {
        private readonly ApplicationDbContext _context;

        public ProductController(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IActionResult> Index(string? search, int? categoryId, int? brandId, 
            decimal? minPrice, decimal? maxPrice, string sortBy = "name", int page = 1, int pageSize = 12)
        {
            var query = _context.Laptops
                .Include(l => l.Brand)
                .Include(l => l.Category)
                .Where(l => l.IsActive);

            // Tìm kiếm theo tên
            if (!string.IsNullOrEmpty(search))
            {
                query = query.Where(l => l.Name.Contains(search) || l.Description.Contains(search));
            }

            // Lọc theo danh mục
            if (categoryId.HasValue)
            {
                query = query.Where(l => l.CategoryId == categoryId.Value);
            }

            // Lọc theo thương hiệu
            if (brandId.HasValue)
            {
                query = query.Where(l => l.BrandId == brandId.Value);
            }

            // Lọc theo giá
            if (minPrice.HasValue)
            {
                query = query.Where(l => (l.SalePrice ?? l.Price) >= minPrice.Value);
            }

            if (maxPrice.HasValue)
            {
                query = query.Where(l => (l.SalePrice ?? l.Price) <= maxPrice.Value);
            }

            // Sắp xếp
            query = sortBy.ToLower() switch
            {
                "price_asc" => query.OrderBy(l => l.SalePrice ?? l.Price),
                "price_desc" => query.OrderByDescending(l => l.SalePrice ?? l.Price),
                "newest" => query.OrderByDescending(l => l.CreatedAt),
                "featured" => query.OrderByDescending(l => l.IsFeatured).ThenBy(l => l.Name),
                _ => query.OrderBy(l => l.Name)
            };

            var totalItems = await query.CountAsync();
            var totalPages = (int)Math.Ceiling(totalItems / (double)pageSize);

            var laptops = await query
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            var viewModel = new ProductListViewModel
            {
                Laptops = laptops,
                Categories = await _context.Categories.Where(c => c.IsActive).OrderBy(c => c.Name).ToListAsync(),
                Brands = await _context.Brands.Where(b => b.IsActive).OrderBy(b => b.Name).ToListAsync(),
                SearchTerm = search,
                CategoryId = categoryId,
                BrandId = brandId,
                MinPrice = minPrice,
                MaxPrice = maxPrice,
                SortBy = sortBy,
                CurrentPage = page,
                TotalPages = totalPages,
                TotalItems = totalItems,
                PageSize = pageSize
            };

            return View(viewModel);
        }

        public async Task<IActionResult> Details(int id)
        {
            var laptop = await _context.Laptops
                .Include(l => l.Brand)
                .Include(l => l.Category)
                .Include(l => l.Reviews)
                    .ThenInclude(r => r.User)
                .FirstOrDefaultAsync(l => l.Id == id && l.IsActive);

            if (laptop == null)
            {
                return NotFound();
            }

            var relatedLaptops = await _context.Laptops
                .Include(l => l.Brand)
                .Include(l => l.Category)
                .Where(l => l.IsActive && l.Id != id && 
                       (l.CategoryId == laptop.CategoryId || l.BrandId == laptop.BrandId))
                .Take(4)
                .ToListAsync();

            var approvedReviews = laptop.Reviews.Where(r => r.IsApproved).ToList();
            var averageRating = approvedReviews.Any() ? approvedReviews.Average(r => r.Rating) : 0;

            var viewModel = new ProductDetailViewModel
            {
                Laptop = laptop,
                RelatedLaptops = relatedLaptops,
                Reviews = approvedReviews,
                AverageRating = averageRating,
                TotalReviews = approvedReviews.Count,
                CanReview = User.Identity?.IsAuthenticated == true
            };

            return View(viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> Search(string term)
        {
            if (string.IsNullOrEmpty(term))
            {
                return Json(new List<object>());
            }

            var laptops = await _context.Laptops
                .Include(l => l.Brand)
                .Where(l => l.IsActive && (l.Name.Contains(term) || l.Brand.Name.Contains(term)))
                .Take(10)
                .Select(l => new
                {
                    id = l.Id,
                    name = l.Name,
                    brand = l.Brand.Name,
                    price = l.SalePrice ?? l.Price,
                    image = l.MainImageUrl
                })
                .ToListAsync();

            return Json(laptops);
        }
    }
}
