#include "SortingAlgorithms.h"
#include <iostream>
#include <iomanip>
#include <random>
#include <algorithm>

// Static member definitions
long long SortingAlgorithms::comparisonCount = 0;
long long SortingAlgorithms::swapCount = 0;

// Reset counters
void SortingAlgorithms::resetCounters() {
    comparisonCount = 0;
    swapCount = 0;
}

// Get counters
long long SortingAlgorithms::getComparisonCount() {
    return comparisonCount;
}

long long SortingAlgorithms::getSwapCount() {
    return swapCount;
}

// Display sorting statistics
void SortingAlgorithms::displayStats(const SortingStats& stats) {
    std::cout << "\n=== THONG KE THUAT TOAN ===" << std::endl;
    std::cout << "Thuat toan: " << stats.algorithmName << std::endl;
    std::cout << "Kich thuoc du lieu: " << stats.dataSize << " phan tu" << std::endl;
    std::cout << "Thoi gian thuc thi: " << stats.executionTime << " microseconds" << std::endl;
    std::cout << "So lan so sanh: " << stats.comparisons << std::endl;
    std::cout << "So lan hoan doi: " << stats.swaps << std::endl;
    std::cout << "=========================" << std::endl;
}

// Compare algorithms
void SortingAlgorithms::compareAlgorithms(const std::vector<SortingStats>& statsVector) {
    if (statsVector.empty()) return;
    
    std::cout << "\n=== SO SANH HIEU SUAT CAC THUAT TOAN ===" << std::endl;
    std::cout << std::left << std::setw(15) << "Thuat toan"
              << std::setw(12) << "Thoi gian"
              << std::setw(12) << "So sanh"
              << std::setw(12) << "Hoan doi"
              << std::setw(10) << "Kich thuoc" << std::endl;
    std::cout << std::string(65, '-') << std::endl;
    
    for (const auto& stats : statsVector) {
        std::cout << std::left << std::setw(15) << stats.algorithmName
                  << std::setw(12) << stats.executionTime
                  << std::setw(12) << stats.comparisons
                  << std::setw(12) << stats.swaps
                  << std::setw(10) << stats.dataSize << std::endl;
    }
    std::cout << std::string(65, '=') << std::endl;
}

// Generate random students
std::vector<Student> SortingAlgorithms::generateRandomStudents(int count) {
    std::vector<Student> students;
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<> scoreDist(0.0, 10.0);
    std::uniform_int_distribution<> yearDist(1995, 2005);
    
    std::vector<std::string> firstNames = {
        "Nguyen", "Tran", "Le", "Pham", "Hoang", "Huynh", "Vo", "Vu", "Dang", "Bui"
    };
    
    std::vector<std::string> lastNames = {
        "Van A", "Thi B", "Van C", "Thi D", "Van E", "Thi F", "Van G", "Thi H", "Van I", "Thi J"
    };
    
    for (int i = 0; i < count; ++i) {
        std::string id = "SV" + std::to_string(1000 + i);
        std::string name = firstNames[i % firstNames.size()] + " " + lastNames[i % lastNames.size()];
        double score = scoreDist(gen);
        int year = yearDist(gen);
        
        students.emplace_back(id, name, score, year);
    }
    
    return students;
}

// Generate sorted students
std::vector<Student> SortingAlgorithms::generateSortedStudents(int count, bool ascending) {
    auto students = generateRandomStudents(count);
    
    if (ascending) {
        std::sort(students.begin(), students.end(), compareByScore);
        std::reverse(students.begin(), students.end()); // Reverse because compareByScore is descending
    } else {
        std::sort(students.begin(), students.end(), compareByScore);
    }
    
    return students;
}

// Generate reverse sorted students
std::vector<Student> SortingAlgorithms::generateReverseSortedStudents(int count) {
    return generateSortedStudents(count, false);
}
