@model LaptopShop.Models.CartViewModel
@{
    ViewData["Title"] = "Giỏ Hàng";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h2 class="gradient-text mb-4">
                <i class="fas fa-shopping-cart me-2"></i>Giỏ Hàng Của Bạn
            </h2>
        </div>
    </div>

    @if (Model.Items.Any())
    {
        <div class="row">
            <!-- Cart Items -->
            <div class="col-lg-8">
                <div class="cart-items">
                    @foreach (var item in Model.Items)
                    {
                        <div class="card mb-3 cart-item" data-item-id="@item.Id">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <!-- Product Image -->
                                    <div class="col-md-2">
                                        @if (!string.IsNullOrEmpty(item.LaptopImage))
                                        {
                                            <img src="@item.LaptopImage" class="img-fluid rounded" alt="@item.LaptopName" style="height: 80px; object-fit: cover;">
                                        }
                                        else
                                        {
                                            <div class="bg-light d-flex align-items-center justify-content-center rounded" style="height: 80px;">
                                                <i class="fas fa-laptop fa-2x text-muted"></i>
                                            </div>
                                        }
                                    </div>

                                    <!-- Product Info -->
                                    <div class="col-md-4">
                                        <h6 class="mb-1">@item.LaptopName</h6>
                                        <div class="price-info">
                                            @if (item.SalePrice.HasValue)
                                            {
                                                <span class="text-danger fw-bold">@item.SalePrice.Value.ToString("N0") ₫</span>
                                                <span class="text-muted text-decoration-line-through ms-2 small">@item.Price.ToString("N0") ₫</span>
                                            }
                                            else
                                            {
                                                <span class="text-primary fw-bold">@item.Price.ToString("N0") ₫</span>
                                            }
                                        </div>
                                        <small class="text-muted">Còn @item.StockQuantity sản phẩm</small>
                                    </div>

                                    <!-- Quantity Controls -->
                                    <div class="col-md-3">
                                        <div class="input-group">
                                            <button class="btn btn-outline-secondary btn-sm" type="button" onclick="updateQuantity(@item.Id, @(item.Quantity - 1))">
                                                <i class="fas fa-minus"></i>
                                            </button>
                                            <input type="number" class="form-control form-control-sm text-center" 
                                                   value="@item.Quantity" min="1" max="@item.StockQuantity" 
                                                   onchange="updateQuantity(@item.Id, this.value)" readonly>
                                            <button class="btn btn-outline-secondary btn-sm" type="button" onclick="updateQuantity(@item.Id, @(item.Quantity + 1))">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Total & Remove -->
                                    <div class="col-md-3 text-end">
                                        <div class="mb-2">
                                            <strong class="item-total">@item.Total.ToString("N0") ₫</strong>
                                        </div>
                                        <button class="btn btn-outline-danger btn-sm" onclick="removeItem(@item.Id)">
                                            <i class="fas fa-trash me-1"></i>Xóa
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>

                <!-- Cart Actions -->
                <div class="cart-actions mt-4">
                    <div class="row">
                        <div class="col-md-6">
                            <a href="@Url.Action("Index", "Product")" class="btn btn-outline-primary">
                                <i class="fas fa-arrow-left me-2"></i>Tiếp tục mua sắm
                            </a>
                        </div>
                        <div class="col-md-6 text-end">
                            <button class="btn btn-outline-danger" onclick="clearCart()">
                                <i class="fas fa-trash me-2"></i>Xóa tất cả
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Summary -->
            <div class="col-lg-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-receipt me-2"></i>Tóm tắt đơn hàng
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="order-summary">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Tạm tính:</span>
                                <span id="subtotal">@Model.SubTotal.ToString("N0") ₫</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Phí vận chuyển:</span>
                                <span id="shipping">
                                    @if (Model.ShippingFee == 0)
                                    {
                                        <span class="text-success">Miễn phí</span>
                                    }
                                    else
                                    {
                                        <span>@Model.ShippingFee.ToString("N0") ₫</span>
                                    }
                                </span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between mb-3">
                                <strong>Tổng cộng:</strong>
                                <strong class="text-primary" id="total">@Model.Total.ToString("N0") ₫</strong>
                            </div>

                            @if (Model.SubTotal < 500000)
                            {
                                <div class="alert alert-info small">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Mua thêm @((500000 - Model.SubTotal).ToString("N0")) ₫ để được miễn phí vận chuyển!
                                </div>
                            }

                            <div class="d-grid gap-2">
                                <a href="@Url.Action("Checkout")" class="btn btn-primary btn-lg">
                                    <i class="fas fa-credit-card me-2"></i>Thanh toán
                                </a>
                                <button class="btn btn-outline-secondary" onclick="saveForLater()">
                                    <i class="fas fa-bookmark me-2"></i>Lưu để mua sau
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Shipping Info -->
                <div class="card mt-3 shadow-sm">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-shipping-fast me-2"></i>Thông tin vận chuyển
                        </h6>
                        <ul class="list-unstyled small mb-0">
                            <li class="mb-1">
                                <i class="fas fa-check text-success me-1"></i>
                                Giao hàng toàn quốc
                            </li>
                            <li class="mb-1">
                                <i class="fas fa-check text-success me-1"></i>
                                Miễn phí ship từ 500.000₫
                            </li>
                            <li class="mb-1">
                                <i class="fas fa-check text-success me-1"></i>
                                Giao hàng trong 1-3 ngày
                            </li>
                            <li>
                                <i class="fas fa-check text-success me-1"></i>
                                Thanh toán khi nhận hàng
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    }
    else
    {
        <!-- Empty Cart -->
        <div class="row">
            <div class="col-12">
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-shopping-cart fa-5x text-muted float-animation"></i>
                    </div>
                    <h4 class="text-muted mb-3">Giỏ hàng của bạn đang trống</h4>
                    <p class="text-muted mb-4">Hãy thêm một số sản phẩm tuyệt vời vào giỏ hàng của bạn!</p>
                    <a href="@Url.Action("Index", "Product")" class="btn btn-primary btn-lg">
                        <i class="fas fa-shopping-bag me-2"></i>Bắt đầu mua sắm
                    </a>
                </div>
            </div>
        </div>
    }
</div>

@section Scripts {
    <script>
        function updateQuantity(cartItemId, newQuantity) {
            if (newQuantity < 1) {
                removeItem(cartItemId);
                return;
            }

            $.post('@Url.Action("UpdateQuantity")', { 
                cartItemId: cartItemId, 
                quantity: newQuantity 
            }, function(data) {
                if (data.success) {
                    // Update cart count in navbar
                    $('#cart-count').text(data.cartCount);
                    
                    // Update totals
                    $('#subtotal').text(data.subTotal + ' ₫');
                    $('#total').text(data.total + ' ₫');
                    
                    // Reload page to update all values
                    location.reload();
                } else {
                    showAlert('danger', data.message);
                }
            });
        }

        function removeItem(cartItemId) {
            if (confirm('Bạn có chắc chắn muốn xóa sản phẩm này khỏi giỏ hàng?')) {
                $.post('@Url.Action("RemoveItem")', { cartItemId: cartItemId }, function(data) {
                    if (data.success) {
                        // Remove item from DOM
                        $('[data-item-id="' + cartItemId + '"]').fadeOut(300, function() {
                            $(this).remove();
                            
                            // Check if cart is empty
                            if ($('.cart-item').length === 0) {
                                location.reload();
                            }
                        });
                        
                        // Update cart count
                        $('#cart-count').text(data.cartCount);
                        
                        // Update totals
                        $('#subtotal').text(data.subTotal + ' ₫');
                        $('#total').text(data.total + ' ₫');
                        
                        showAlert('success', 'Đã xóa sản phẩm khỏi giỏ hàng');
                    } else {
                        showAlert('danger', data.message);
                    }
                });
            }
        }

        function clearCart() {
            if (confirm('Bạn có chắc chắn muốn xóa tất cả sản phẩm khỏi giỏ hàng?')) {
                $.post('@Url.Action("ClearCart")', function(data) {
                    if (data.success) {
                        location.reload();
                    } else {
                        showAlert('danger', 'Có lỗi xảy ra khi xóa giỏ hàng');
                    }
                });
            }
        }

        function saveForLater() {
            showAlert('info', 'Tính năng này sẽ được cập nhật trong phiên bản tiếp theo!');
        }
    </script>
}
