/* _content/ShoeShopAnNhien/Views/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-y6g7bms38p] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-y6g7bms38p] {
  color: #0077cc;
}

.btn-primary[b-y6g7bms38p] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-y6g7bms38p], .nav-pills .show > .nav-link[b-y6g7bms38p] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-y6g7bms38p] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-y6g7bms38p] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-y6g7bms38p] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-y6g7bms38p] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-y6g7bms38p] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
