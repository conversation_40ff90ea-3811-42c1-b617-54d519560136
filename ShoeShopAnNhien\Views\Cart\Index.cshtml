@model ShoeShopAnNhien.ViewModels.CartViewModel
@{
    ViewData["Title"] = "Giỏ Hàng";
}

<!-- Hero Section -->
<section class="hero-section bg-primary text-white py-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-5 fw-bold mb-2">Giỏ Hàng Của Bạn</h1>
                <p class="lead mb-0">Xem lại các sản phẩm bạn đã chọn</p>
            </div>
            <div class="col-lg-4 text-end">
                <i class="fas fa-shopping-cart" style="font-size: 4rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
</section>

<!-- Cart Content -->
<section class="py-5">
    <div class="container">
        @if (Model.CartItems.Any())
        {
            <div class="row">
                <!-- Cart Items -->
                <div class="col-lg-8">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white">
                            <h5 class="mb-0 fw-bold">Sản Phẩm Trong Giỏ Hàng (@Model.CartItems.Count sản phẩm)</h5>
                        </div>
                        <div class="card-body p-0">
                            @foreach (var item in Model.CartItems)
                            {
                                <div class="cart-item border-bottom p-4" data-cart-item-id="@item.Id">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <img src="@(item.Product.MainImageUrl ?? "/images/products/default.jpg")" 
                                                 alt="@item.Product.Name" 
                                                 class="img-fluid rounded" 
                                                 style="width: 80px; height: 80px; object-fit: cover;">
                                        </div>
                                        <div class="col-md-4">
                                            <h6 class="fw-bold mb-1">@item.Product.Name</h6>
                                            <p class="text-muted small mb-1">@item.Product.Brand.Name</p>
                                            @if (!string.IsNullOrEmpty(item.Size))
                                            {
                                                <span class="badge bg-light text-dark me-1">Size: @item.Size</span>
                                            }
                                            @if (!string.IsNullOrEmpty(item.Color))
                                            {
                                                <span class="badge bg-light text-dark">Màu: @item.Color</span>
                                            }
                                        </div>
                                        <div class="col-md-2">
                                            <div class="price-section">
                                                @if (item.Product.IsOnSale)
                                                {
                                                    <span class="text-danger fw-bold">@item.Product.SalePrice?.ToString("N0") VNĐ</span>
                                                    <br><small class="text-muted text-decoration-line-through">@item.Product.Price.ToString("N0") VNĐ</small>
                                                }
                                                else
                                                {
                                                    <span class="text-primary fw-bold">@item.Product.Price.ToString("N0") VNĐ</span>
                                                }
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="quantity-controls d-flex align-items-center">
                                                <button class="btn btn-outline-secondary btn-sm" onclick="updateQuantity(@item.Id, @(item.Quantity - 1))">
                                                    <i class="fas fa-minus"></i>
                                                </button>
                                                <span class="mx-3 fw-bold quantity-display">@item.Quantity</span>
                                                <button class="btn btn-outline-secondary btn-sm" onclick="updateQuantity(@item.Id, @(item.Quantity + 1))">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="col-md-2 text-end">
                                            <div class="total-price fw-bold text-primary mb-2">
                                                @item.TotalPrice.ToString("N0") VNĐ
                                            </div>
                                            <button class="btn btn-outline-danger btn-sm" onclick="removeItem(@item.Id)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>

                    <!-- Continue Shopping -->
                    <div class="mt-4">
                        <a href="@Url.Action("Index", "Products")" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-2"></i>Tiếp Tục Mua Sắm
                        </a>
                    </div>
                </div>

                <!-- Order Summary -->
                <div class="col-lg-4">
                    <div class="card border-0 shadow-sm sticky-top" style="top: 2rem;">
                        <div class="card-header bg-white">
                            <h5 class="mb-0 fw-bold">Tóm Tắt Đơn Hàng</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Tạm tính:</span>
                                <span class="subtotal">@Model.SubTotal.ToString("N0") VNĐ</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Phí vận chuyển:</span>
                                <span>@Model.ShippingFee.ToString("N0") VNĐ</span>
                            </div>
                            @if (Model.Discount > 0)
                            {
                                <div class="d-flex justify-content-between mb-2 text-success">
                                    <span>Giảm giá:</span>
                                    <span>-@Model.Discount.ToString("N0") VNĐ</span>
                                </div>
                            }
                            <hr>
                            <div class="d-flex justify-content-between mb-3">
                                <strong>Tổng cộng:</strong>
                                <strong class="text-primary total-amount">@Model.TotalAmount.ToString("N0") VNĐ</strong>
                            </div>

                            <!-- Coupon Code -->
                            <div class="mb-3">
                                <label class="form-label small">Mã giảm giá:</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="couponCode" placeholder="Nhập mã giảm giá">
                                    <button class="btn btn-outline-secondary" type="button" onclick="applyCoupon()">
                                        Áp dụng
                                    </button>
                                </div>
                            </div>

                            <!-- Checkout Button -->
                            <button class="btn btn-primary w-100 btn-lg" onclick="proceedToCheckout()">
                                <i class="fas fa-credit-card me-2"></i>Thanh Toán
                            </button>

                            <!-- Payment Methods -->
                            <div class="mt-3 text-center">
                                <small class="text-muted">Chúng tôi chấp nhận:</small>
                                <div class="mt-2">
                                    <i class="fab fa-cc-visa fs-4 text-primary me-2"></i>
                                    <i class="fab fa-cc-mastercard fs-4 text-warning me-2"></i>
                                    <i class="fas fa-mobile-alt fs-4 text-success me-2"></i>
                                    <i class="fas fa-university fs-4 text-info"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
        else
        {
            <!-- Empty Cart -->
            <div class="text-center py-5">
                <div class="mb-4">
                    <i class="fas fa-shopping-cart text-muted" style="font-size: 6rem; opacity: 0.3;"></i>
                </div>
                <h3 class="fw-bold mb-3">Giỏ Hàng Trống</h3>
                <p class="text-muted mb-4">Bạn chưa có sản phẩm nào trong giỏ hàng. Hãy khám phá các sản phẩm tuyệt vời của chúng tôi!</p>
                <a href="@Url.Action("Index", "Products")" class="btn btn-primary btn-lg">
                    <i class="fas fa-shopping-bag me-2"></i>Bắt Đầu Mua Sắm
                </a>
            </div>
        }
    </div>
</section>

<script>
function updateQuantity(cartItemId, newQuantity) {
    if (newQuantity < 0) return;
    
    fetch('/Cart/UpdateQuantity', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `cartItemId=${cartItemId}&quantity=${newQuantity}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (newQuantity === 0) {
                location.reload();
            } else {
                // Update quantity display and recalculate totals
                location.reload();
            }
        } else {
            showToast(data.message || 'Có lỗi xảy ra!', 'error');
        }
    })
    .catch(error => {
        showToast('Có lỗi xảy ra khi cập nhật giỏ hàng!', 'error');
    });
}

function removeItem(cartItemId) {
    if (!confirm('Bạn có chắc chắn muốn xóa sản phẩm này khỏi giỏ hàng?')) {
        return;
    }
    
    fetch('/Cart/RemoveItem', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `cartItemId=${cartItemId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            showToast(data.message || 'Có lỗi xảy ra!', 'error');
        }
    })
    .catch(error => {
        showToast('Có lỗi xảy ra khi xóa sản phẩm!', 'error');
    });
}

function applyCoupon() {
    const couponCode = document.getElementById('couponCode').value.trim();
    if (!couponCode) {
        showToast('Vui lòng nhập mã giảm giá!', 'warning');
        return;
    }
    
    // Simulate coupon application
    showToast('Mã giảm giá không hợp lệ hoặc đã hết hạn!', 'error');
}

function proceedToCheckout() {
    @if (Model.CartItems.Any())
    {
        <text>
        // Redirect to checkout page
        window.location.href = '/Checkout';
        </text>
    }
    else
    {
        <text>
        showToast('Giỏ hàng trống! Vui lòng thêm sản phẩm trước khi thanh toán.', 'warning');
        </text>
    }
}
</script>
