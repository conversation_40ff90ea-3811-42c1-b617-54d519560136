@model LaptopShop.Models.ProductListViewModel
@{
    ViewData["Title"] = "Danh Sách Laptop";
}

<div class="container-fluid mt-4">
    <div class="row">
        <!-- Filters Sidebar -->
        <div class="col-lg-3 mb-4">
            <div class="filters-sidebar">
                <h5 class="mb-3 gradient-text"><i class="fas fa-filter me-2"></i>Bộ Lọc</h5>
                
                <form method="get" asp-action="Index">
                    <!-- Search -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">T<PERSON><PERSON> kiếm</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" name="search" value="@Model.SearchTerm" class="form-control" placeholder="Tên laptop, thương hiệu...">
                        </div>
                    </div>

                    <!-- Category Filter -->
                    <div class="mb-3">
                        <label class="form-label">Danh mục</label>
                        <select name="categoryId" class="form-select">
                            <option value="">Tất cả danh mục</option>
                            @foreach (var category in Model.Categories)
                            {
                                <option value="@category.Id" selected="@(Model.CategoryId == category.Id)">
                                    @category.Name
                                </option>
                            }
                        </select>
                    </div>

                    <!-- Brand Filter -->
                    <div class="mb-3">
                        <label class="form-label">Thương hiệu</label>
                        <select name="brandId" class="form-select">
                            <option value="">Tất cả thương hiệu</option>
                            @foreach (var brand in Model.Brands)
                            {
                                <option value="@brand.Id" selected="@(Model.BrandId == brand.Id)">
                                    @brand.Name
                                </option>
                            }
                        </select>
                    </div>

                    <!-- Price Range -->
                    <div class="mb-3">
                        <label class="form-label">Khoảng giá</label>
                        <div class="row">
                            <div class="col-6">
                                <input type="number" name="minPrice" value="@Model.MinPrice" class="form-control" placeholder="Từ" min="0">
                            </div>
                            <div class="col-6">
                                <input type="number" name="maxPrice" value="@Model.MaxPrice" class="form-control" placeholder="Đến" min="0">
                            </div>
                        </div>
                    </div>

                    <!-- Sort -->
                    <div class="mb-3">
                        <label class="form-label">Sắp xếp</label>
                        <select name="sortBy" class="form-select">
                            <option value="name" selected="@(Model.SortBy == "name")">Tên A-Z</option>
                            <option value="price_asc" selected="@(Model.SortBy == "price_asc")">Giá thấp đến cao</option>
                            <option value="price_desc" selected="@(Model.SortBy == "price_desc")">Giá cao đến thấp</option>
                            <option value="newest" selected="@(Model.SortBy == "newest")">Mới nhất</option>
                            <option value="featured" selected="@(Model.SortBy == "featured")">Nổi bật</option>
                        </select>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Lọc
                        </button>
                        <a href="@Url.Action("Index")" class="btn btn-outline-secondary">
                            <i class="fas fa-undo me-2"></i>Xóa bộ lọc
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Products Grid -->
        <div class="col-lg-9">
            <!-- Results Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="gradient-text mb-2">Danh Sách Laptop</h2>
                    <p class="text-muted mb-0">
                        <i class="fas fa-laptop me-1"></i>
                        Tìm thấy <strong>@Model.TotalItems</strong> sản phẩm
                    </p>
                </div>
                <div class="d-flex align-items-center">
                    <span class="me-2 fw-bold">Hiển thị:</span>
                    <select class="form-select form-select-sm" style="width: auto;" onchange="changePageSize(this.value)">
                        <option value="12" selected="@(Model.PageSize == 12)">12</option>
                        <option value="24" selected="@(Model.PageSize == 24)">24</option>
                        <option value="48" selected="@(Model.PageSize == 48)">48</option>
                    </select>
                </div>
            </div>

            <!-- Products Grid -->
            @if (Model.Laptops.Any())
            {
                <div class="row">
                    @foreach (var laptop in Model.Laptops)
                    {
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100 shadow-sm product-card">
                                <div class="position-relative img-hover-zoom">
                                    @if (!string.IsNullOrEmpty(laptop.MainImageUrl))
                                    {
                                        <img src="@laptop.MainImageUrl" class="card-img-top" alt="@laptop.Name" style="height: 220px; object-fit: cover;">
                                    }
                                    else
                                    {
                                        <div class="bg-light d-flex align-items-center justify-content-center" style="height: 220px;">
                                            <i class="fas fa-laptop fa-3x text-muted float-animation"></i>
                                        </div>
                                    }

                                    <!-- Badges -->
                                    <div class="position-absolute top-0 end-0 m-2">
                                        @if (laptop.SalePrice.HasValue)
                                        {
                                            <span class="badge bg-danger mb-1 d-block">
                                                <i class="fas fa-percentage me-1"></i>-@(Math.Round((1 - laptop.SalePrice.Value / laptop.Price) * 100))%
                                            </span>
                                        }
                                        @if (laptop.IsFeatured)
                                        {
                                            <span class="badge bg-warning">
                                                <i class="fas fa-star me-1"></i>Nổi bật
                                            </span>
                                        }
                                    </div>

                                    @if (laptop.StockQuantity <= 5)
                                    {
                                        <div class="position-absolute top-0 start-0 m-2">
                                            <span class="badge bg-danger">
                                                <i class="fas fa-exclamation-triangle me-1"></i>Sắp hết
                                            </span>
                                        </div>
                                    }
                                </div>
                                <div class="card-body">
                                    <h6 class="card-title fw-bold">@laptop.Name</h6>
                                    <p class="text-muted small mb-1">
                                        <i class="fas fa-tag me-1"></i>@laptop.Brand.Name - @laptop.Category.Name
                                    </p>
                                    <p class="text-muted small mb-2">
                                        <i class="fas fa-microchip me-1"></i>@laptop.Processor
                                    </p>

                                    <div class="price-section mb-3">
                                        @if (laptop.SalePrice.HasValue)
                                        {
                                            <div class="d-flex align-items-center">
                                                <span class="h6 text-danger mb-0 me-2">@laptop.SalePrice.Value.ToString("N0") ₫</span>
                                                <span class="text-muted text-decoration-line-through small">@laptop.Price.ToString("N0") ₫</span>
                                            </div>
                                        }
                                        else
                                        {
                                            <span class="h6 text-primary mb-0">@laptop.Price.ToString("N0") ₫</span>
                                        }
                                    </div>

                                    <div class="d-grid gap-2">
                                        <a href="@Url.Action("Details", new { id = laptop.Id })" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye me-1"></i>Xem Chi Tiết
                                        </a>
                                        @if (laptop.StockQuantity > 0)
                                        {
                                            <button class="btn btn-primary btn-sm" onclick="addToCart(@laptop.Id)">
                                                <i class="fas fa-cart-plus me-1"></i>Thêm Vào Giỏ
                                            </button>
                                        }
                                        else
                                        {
                                            <button class="btn btn-secondary btn-sm" disabled>
                                                <i class="fas fa-times me-1"></i>Hết Hàng
                                            </button>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>

                <!-- Pagination -->
                @if (Model.TotalPages > 1)
                {
                    <nav aria-label="Phân trang" class="mt-4">
                        <ul class="pagination justify-content-center">
                            @if (Model.CurrentPage > 1)
                            {
                                <li class="page-item">
                                    <a class="page-link" href="@GetPageUrl(1)">
                                        <i class="fas fa-angle-double-left"></i> Đầu
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="@GetPageUrl(Model.CurrentPage - 1)">
                                        <i class="fas fa-angle-left"></i> Trước
                                    </a>
                                </li>
                            }

                            @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                            {
                                <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                    <a class="page-link" href="@GetPageUrl(i)">@i</a>
                                </li>
                            }

                            @if (Model.CurrentPage < Model.TotalPages)
                            {
                                <li class="page-item">
                                    <a class="page-link" href="@GetPageUrl(Model.CurrentPage + 1)">
                                        Sau <i class="fas fa-angle-right"></i>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="@GetPageUrl(Model.TotalPages)">
                                        Cuối <i class="fas fa-angle-double-right"></i>
                                    </a>
                                </li>
                            }
                        </ul>
                    </nav>
                }
            }
            else
            {
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-search fa-4x text-muted float-animation"></i>
                    </div>
                    <h4 class="text-muted">Không tìm thấy sản phẩm nào</h4>
                    <p class="text-muted">Vui lòng thử lại với từ khóa khác hoặc điều chỉnh bộ lọc</p>
                    <a href="@Url.Action("Index")" class="btn btn-primary">
                        <i class="fas fa-undo me-2"></i>Xem tất cả sản phẩm
                    </a>
                </div>
            }
        </div>
    </div>
</div>

@functions {
    string GetPageUrl(int page)
    {
        var routeValues = new RouteValueDictionary
        {
            ["page"] = page,
            ["search"] = Model.SearchTerm,
            ["categoryId"] = Model.CategoryId,
            ["brandId"] = Model.BrandId,
            ["minPrice"] = Model.MinPrice,
            ["maxPrice"] = Model.MaxPrice,
            ["sortBy"] = Model.SortBy,
            ["pageSize"] = Model.PageSize
        };
        return Url.Action("Index", routeValues);
    }
}

@section Scripts {
    <script>
        function changePageSize(pageSize) {
            var url = new URL(window.location);
            url.searchParams.set('pageSize', pageSize);
            url.searchParams.set('page', '1');
            window.location.href = url.toString();
        }
    </script>
}
