@model BubbleTeaCafe.Models.Product

@{
    ViewData["Title"] = "Thêm Sản Phẩm Mới";
}

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-plus-circle me-2 text-primary"></i>Thêm Sản Phẩm Mới
                </h1>
                <a href="@Url.Action("Products", "Admin")" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Quay Lại
                </a>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Thông Tin Sản Phẩm</h5>
                        </div>
                        <div class="card-body">
                            <form asp-action="AddProduct" method="post" enctype="multipart/form-data">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label asp-for="Name" class="form-label">Tên Sản Phẩm *</label>
                                            <input asp-for="Name" class="form-control" placeholder="Nhập tên sản phẩm">
                                            <span asp-validation-for="Name" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label asp-for="CategoryId" class="form-label">Danh Mục *</label>
                                            <select asp-for="CategoryId" class="form-select">
                                                <option value="">Chọn danh mục</option>
                                                <option value="1">Trà Sữa</option>
                                                <option value="2">Cà Phê</option>
                                                <option value="3">Trà Trái Cây</option>
                                                <option value="4">Sinh Tố</option>
                                            </select>
                                            <span asp-validation-for="CategoryId" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="Description" class="form-label">Mô Tả</label>
                                    <textarea asp-for="Description" class="form-control" rows="4" placeholder="Nhập mô tả sản phẩm"></textarea>
                                    <span asp-validation-for="Description" class="text-danger"></span>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label asp-for="Price" class="form-label">Giá (VNĐ) *</label>
                                            <input asp-for="Price" type="number" class="form-control" placeholder="0" min="0" step="1000">
                                            <span asp-validation-for="Price" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Trạng Thái</label>
                                            <select asp-for="IsAvailable" class="form-select">
                                                <option value="true">Có sẵn</option>
                                                <option value="false">Hết hàng</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Hình Ảnh</label>
                                            <input type="file" class="form-control" accept="image/*" id="productImage">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="Ingredients" class="form-label">Nguyên Liệu</label>
                                    <textarea asp-for="Ingredients" class="form-control" rows="3" placeholder="Nhập danh sách nguyên liệu, cách nhau bởi dấu phẩy"></textarea>
                                    <span asp-validation-for="Ingredients" class="text-danger"></span>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label asp-for="SizeOptions" class="form-label">Tùy Chọn Size</label>
                                            <textarea asp-for="SizeOptions" class="form-control" rows="2" placeholder="S, M, L"></textarea>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label asp-for="SweetnessOptions" class="form-label">Tùy Chọn Độ Ngọt</label>
                                            <textarea asp-for="SweetnessOptions" class="form-control" rows="2" placeholder="0%, 25%, 50%, 75%, 100%"></textarea>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label asp-for="TemperatureOptions" class="form-label">Tùy Chọn Nhiệt Độ</label>
                                            <textarea asp-for="TemperatureOptions" class="form-control" rows="2" placeholder="Nóng, Ấm, Lạnh, Đá"></textarea>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-end gap-2">
                                    <a href="@Url.Action("Products", "Admin")" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>Hủy
                                    </a>
                                    <button type="button" class="btn btn-outline-primary" onclick="previewProduct()">
                                        <i class="fas fa-eye me-2"></i>Xem Trước
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Lưu Sản Phẩm
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Preview Card -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Xem Trước</h5>
                        </div>
                        <div class="card-body">
                            <div class="product-preview">
                                <div class="preview-image mb-3">
                                    <img id="previewImg" src="https://via.placeholder.com/300x200?text=Chọn+Hình+Ảnh" 
                                         alt="Preview" class="img-fluid rounded">
                                </div>
                                <h5 id="previewName" class="text-muted">Tên sản phẩm sẽ hiển thị ở đây</h5>
                                <p id="previewDescription" class="text-muted small">Mô tả sản phẩm sẽ hiển thị ở đây</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span id="previewPrice" class="h5 text-primary">0đ</span>
                                    <span id="previewStatus" class="badge bg-secondary">Chưa xác định</span>
                                </div>
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <strong>Nguyên liệu:</strong> 
                                        <span id="previewIngredients">Chưa có thông tin</span>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tips Card -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-lightbulb me-2"></i>Gợi Ý
                            </h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Sử dụng tên sản phẩm ngắn gọn, dễ nhớ
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Mô tả chi tiết về hương vị và đặc điểm
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Hình ảnh chất lượng cao, tỷ lệ 3:2
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Giá cả hợp lý so với thị trường
                                </li>
                                <li>
                                    <i class="fas fa-check text-success me-2"></i>
                                    Liệt kê đầy đủ nguyên liệu chính
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Real-time preview
        document.addEventListener('DOMContentLoaded', function() {
            // Update preview when inputs change
            document.querySelector('input[name="Name"]').addEventListener('input', function() {
                document.getElementById('previewName').textContent = this.value || 'Tên sản phẩm sẽ hiển thị ở đây';
            });

            document.querySelector('textarea[name="Description"]').addEventListener('input', function() {
                document.getElementById('previewDescription').textContent = this.value || 'Mô tả sản phẩm sẽ hiển thị ở đây';
            });

            document.querySelector('input[name="Price"]').addEventListener('input', function() {
                const price = this.value ? parseInt(this.value).toLocaleString('vi-VN') + 'đ' : '0đ';
                document.getElementById('previewPrice').textContent = price;
            });

            document.querySelector('select[name="IsAvailable"]').addEventListener('change', function() {
                const status = this.value === 'true' ? 'Có sẵn' : 'Hết hàng';
                const badgeClass = this.value === 'true' ? 'bg-success' : 'bg-danger';
                const statusElement = document.getElementById('previewStatus');
                statusElement.textContent = status;
                statusElement.className = 'badge ' + badgeClass;
            });

            document.querySelector('textarea[name="Ingredients"]').addEventListener('input', function() {
                document.getElementById('previewIngredients').textContent = this.value || 'Chưa có thông tin';
            });

            // Image preview
            document.getElementById('productImage').addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        document.getElementById('previewImg').src = e.target.result;
                    };
                    reader.readAsDataURL(file);
                }
            });
        });

        function previewProduct() {
            alert('Xem trước sản phẩm trong card bên phải');
        }

        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const name = document.querySelector('input[name="Name"]').value;
            const category = document.querySelector('select[name="CategoryId"]').value;
            const price = document.querySelector('input[name="Price"]').value;

            if (!name || !category || !price) {
                e.preventDefault();
                alert('Vui lòng điền đầy đủ thông tin bắt buộc (Tên, Danh mục, Giá)');
                return false;
            }

            if (parseInt(price) <= 0) {
                e.preventDefault();
                alert('Giá sản phẩm phải lớn hơn 0');
                return false;
            }
        });
    </script>

    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
