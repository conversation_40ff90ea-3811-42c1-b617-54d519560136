﻿<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - LaptopShop - C<PERSON>a <PERSON>ptop Uy Tín</title>
    <script type="importmap"></script>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/LaptopShop.styles.css" asp-append-version="true" />
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand fw-bold" asp-controller="Home" asp-action="Index">
                    <i class="fas fa-laptop me-2"></i>LaptopShop
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Home" asp-action="Index">
                                <i class="fas fa-home me-1"></i>Trang Chủ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Product" asp-action="Index">
                                <i class="fas fa-laptop me-1"></i>Sản Phẩm
                            </a>
                        </li>
                    </ul>

                    <!-- Search Form -->
                    <form class="d-flex me-3" asp-controller="Product" asp-action="Index" method="get">
                        <div class="input-group">
                            <input class="form-control" type="search" name="search" placeholder="Tìm kiếm laptop..." value="@ViewBag.Search">
                            <button class="btn btn-outline-light" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>

                    <ul class="navbar-nav">
                        @if (User.Identity?.IsAuthenticated == true)
                        {
                            <li class="nav-item">
                                <a class="nav-link position-relative" asp-controller="Cart" asp-action="Index">
                                    <i class="fas fa-shopping-cart me-1"></i>Giỏ Hàng
                                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="cart-count">
                                        0
                                    </span>
                                </a>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user me-1"></i>@User.Identity.Name
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" asp-controller="Account" asp-action="Profile">
                                        <i class="fas fa-user-edit me-1"></i>Thông Tin Cá Nhân
                                    </a></li>
                                    @if (User.IsInRole("Admin"))
                                    {
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" asp-controller="Admin" asp-action="Index">
                                            <i class="fas fa-cog me-1"></i>Quản Trị
                                        </a></li>
                                    }
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form asp-controller="Account" asp-action="Logout" method="post" class="d-inline">
                                            <button type="submit" class="dropdown-item">
                                                <i class="fas fa-sign-out-alt me-1"></i>Đăng Xuất
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        }
                        else
                        {
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Account" asp-action="Login">
                                    <i class="fas fa-sign-in-alt me-1"></i>Đăng Nhập
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Account" asp-action="Register">
                                    <i class="fas fa-user-plus me-1"></i>Đăng Ký
                                </a>
                            </li>
                        }
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    <main role="main" class="flex-grow-1">
        @if (TempData["SuccessMessage"] != null)
        {
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                @TempData["SuccessMessage"]
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        }
        @if (TempData["ErrorMessage"] != null)
        {
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                @TempData["ErrorMessage"]
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        }
        @RenderBody()
    </main>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    <script>
        // Load cart count on page load
        $(document).ready(function() {
            @if (User.Identity?.IsAuthenticated == true)
            {
                <text>
                loadCartCount();
                </text>
            }
        });

        function loadCartCount() {
            $.get('@Url.Action("GetCartCount", "Cart")', function(data) {
                $('#cart-count').text(data.cartCount);
                if (data.cartCount > 0) {
                    $('#cart-count').show();
                } else {
                    $('#cart-count').hide();
                }
            });
        }

        // Add to cart function
        function addToCart(laptopId, quantity = 1) {
            @if (User.Identity?.IsAuthenticated == true)
            {
                <text>
                $.post('@Url.Action("AddToCart", "Cart")', { laptopId: laptopId, quantity: quantity }, function(data) {
                    if (data.success) {
                        $('#cart-count').text(data.cartCount);
                        $('#cart-count').show();

                        // Show success message
                        showAlert('success', data.message);
                    } else {
                        showAlert('danger', data.message);
                    }
                });
                </text>
            }
            else
            {
                <text>
                window.location.href = '@Url.Action("Login", "Account")';
                </text>
            }
        }

        function showAlert(type, message) {
            var alertHtml = '<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' +
                           message +
                           '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                           '</div>';
            $('main').prepend(alertHtml);

            // Auto dismiss after 3 seconds
            setTimeout(function() {
                $('.alert').alert('close');
            }, 3000);
        }
    </script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
