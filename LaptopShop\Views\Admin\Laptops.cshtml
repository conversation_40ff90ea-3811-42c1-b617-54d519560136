@model List<LaptopShop.Models.Laptop>
@{
    ViewData["Title"] = "Quản Lý Laptop";
}

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 admin-sidebar">
            <div class="d-flex flex-column p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-laptop me-2"></i>LaptopShop Admin
                </h5>
                
                <ul class="nav nav-pills flex-column">
                    <li class="nav-item">
                        <a class="nav-link" asp-action="Index">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" asp-action="Laptops">
                            <i class="fas fa-laptop me-2"></i>Quản lý Laptop
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-action="Orders">
                            <i class="fas fa-shopping-cart me-2"></i>Quản lý Đơn hàng
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-action="Customers">
                            <i class="fas fa-users me-2"></i>Quản lý Khách hàng
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-action="Categories">
                            <i class="fas fa-tags me-2"></i>Danh mục
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-action="Brands">
                            <i class="fas fa-trademark me-2"></i>Thương hiệu
                        </a>
                    </li>
                </ul>

                <hr class="text-white">
                
                <div class="mt-auto">
                    <a class="nav-link text-light" asp-controller="Home" asp-action="Index" target="_blank">
                        <i class="fas fa-external-link-alt me-2"></i>Xem Website
                    </a>
                    <a class="nav-link text-light" asp-action="Logout">
                        <i class="fas fa-sign-out-alt me-2"></i>Đăng xuất
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <div class="p-4">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="gradient-text">Quản Lý Laptop</h2>
                    <button class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Thêm Laptop Mới
                    </button>
                </div>

                <!-- Search & Filter -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="Tìm kiếm laptop...">
                            <button class="btn btn-outline-secondary" type="button">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select">
                            <option value="">Tất cả danh mục</option>
                            <option value="1">Gaming</option>
                            <option value="2">Văn phòng</option>
                            <option value="3">Đồ họa</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select">
                            <option value="">Tất cả thương hiệu</option>
                            <option value="1">ASUS</option>
                            <option value="2">Dell</option>
                            <option value="3">HP</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select">
                            <option value="">Trạng thái</option>
                            <option value="active">Hoạt động</option>
                            <option value="inactive">Ngừng bán</option>
                        </select>
                    </div>
                </div>

                <!-- Laptops Table -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th>Hình ảnh</th>
                                        <th>Tên Laptop</th>
                                        <th>Thương hiệu</th>
                                        <th>Danh mục</th>
                                        <th>Giá</th>
                                        <th>Tồn kho</th>
                                        <th>Trạng thái</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (Model != null && Model.Any())
                                    {
                                        @foreach (var laptop in Model)
                                        {
                                            <tr>
                                                <td>@laptop.Id</td>
                                                <td>
                                                    @if (!string.IsNullOrEmpty(laptop.MainImageUrl))
                                                    {
                                                        <img src="@laptop.MainImageUrl" alt="@laptop.Name" style="width: 50px; height: 50px; object-fit: cover;" class="rounded">
                                                    }
                                                    else
                                                    {
                                                        <div class="bg-light d-flex align-items-center justify-content-center rounded" style="width: 50px; height: 50px;">
                                                            <i class="fas fa-laptop text-muted"></i>
                                                        </div>
                                                    }
                                                </td>
                                                <td>
                                                    <strong>@laptop.Name</strong>
                                                    @if (laptop.IsFeatured)
                                                    {
                                                        <span class="badge bg-warning ms-1">Nổi bật</span>
                                                    }
                                                </td>
                                                <td>@laptop.Brand.Name</td>
                                                <td>@laptop.Category.Name</td>
                                                <td>
                                                    @if (laptop.SalePrice.HasValue)
                                                    {
                                                        <span class="text-danger fw-bold">@laptop.SalePrice.Value.ToString("N0") ₫</span>
                                                        <br>
                                                        <small class="text-muted text-decoration-line-through">@laptop.Price.ToString("N0") ₫</small>
                                                    }
                                                    else
                                                    {
                                                        <span class="fw-bold">@laptop.Price.ToString("N0") ₫</span>
                                                    }
                                                </td>
                                                <td>
                                                    <span class="badge @(laptop.StockQuantity > 10 ? "bg-success" : laptop.StockQuantity > 0 ? "bg-warning" : "bg-danger")">
                                                        @laptop.StockQuantity
                                                    </span>
                                                </td>
                                                <td>
                                                    @if (laptop.IsActive)
                                                    {
                                                        <span class="badge bg-success">Hoạt động</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-secondary">Ngừng bán</span>
                                                    }
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Xem">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="#" class="btn btn-sm btn-outline-warning" title="Sửa">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <button class="btn btn-sm btn-outline-danger" title="Xóa">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                    }
                                    else
                                    {
                                        <tr>
                                            <td colspan="9" class="text-center py-4">
                                                <i class="fas fa-laptop fa-3x text-muted mb-3"></i>
                                                <p class="text-muted">Chưa có laptop nào trong hệ thống</p>
                                                <button class="btn btn-primary">
                                                    <i class="fas fa-plus me-2"></i>Thêm Laptop Đầu Tiên
                                                </button>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                @if (Model != null && Model.Any())
                {
                    <nav aria-label="Laptop pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1">Trước</a>
                            </li>
                            <li class="page-item active">
                                <a class="page-link" href="#">1</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">2</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">3</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">Sau</a>
                            </li>
                        </ul>
                    </nav>
                }
            </div>
        </div>
    </div>
</div>
