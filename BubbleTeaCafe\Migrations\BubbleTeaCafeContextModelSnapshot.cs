﻿// <auto-generated />
using System;
using BubbleTeaCafe.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace BubbleTeaCafe.Migrations
{
    [DbContext(typeof(BubbleTeaCafeContext))]
    partial class BubbleTeaCafeContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "9.0.7");

            modelBuilder.Entity("BubbleTeaCafe.Models.Category", b =>
                {
                    b.Property<int>("CategoryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.<PERSON>("CategoryId");

                    b.To<PERSON>("Categories");

                    b.HasData(
                        new
                        {
                            CategoryId = 1,
                            Description = "Bubble Tea với nhiều hương vị thơm ngon",
                            Name = "Trà Sữa"
                        },
                        new
                        {
                            CategoryId = 2,
                            Description = "Cà phê rang xay thơm ngon",
                            Name = "Cà Phê"
                        },
                        new
                        {
                            CategoryId = 3,
                            Description = "Trà trái cây tươi mát",
                            Name = "Trà Trái Cây"
                        },
                        new
                        {
                            CategoryId = 4,
                            Description = "Sinh tố trái cây tươi ngon",
                            Name = "Smoothie"
                        });
                });

            modelBuilder.Entity("BubbleTeaCafe.Models.Order", b =>
                {
                    b.Property<int>("OrderId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("CustomerEmail")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("CustomerName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("CustomerPhone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("DeliveryAddress")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("OrderDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("OrderType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("OrderId");

                    b.ToTable("Orders");
                });

            modelBuilder.Entity("BubbleTeaCafe.Models.OrderItem", b =>
                {
                    b.Property<int>("OrderItemId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("OrderId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("ProductId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Quantity")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Size")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("SpecialInstructions")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Sweetness")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("Temperature")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("OrderItemId");

                    b.HasIndex("OrderId");

                    b.HasIndex("ProductId");

                    b.ToTable("OrderItems");
                });

            modelBuilder.Entity("BubbleTeaCafe.Models.Product", b =>
                {
                    b.Property<int>("ProductId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("CategoryId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("ImageUrl")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Ingredients")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsAvailable")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("SizeOptions")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("SweetnessOptions")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("TemperatureOptions")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("ProductId");

                    b.HasIndex("CategoryId");

                    b.ToTable("Products");

                    b.HasData(
                        new
                        {
                            ProductId = 1,
                            CategoryId = 1,
                            Description = "Trà sữa đậm đà với trân châu đen",
                            ImageUrl = "/images/tra-sua-truyen-thong.svg",
                            Ingredients = "Trà đen, sữa tươi, trân châu đen",
                            IsAvailable = true,
                            Name = "Trà Sữa Truyền Thống",
                            Price = 35000m,
                            SizeOptions = "M,L",
                            SweetnessOptions = "0%,25%,50%,75%,100%",
                            TemperatureOptions = "Cold,Hot"
                        },
                        new
                        {
                            ProductId = 2,
                            CategoryId = 1,
                            Description = "Trà sữa matcha Nhật Bản thơm ngon",
                            ImageUrl = "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=400&fit=crop&crop=center",
                            Ingredients = "Matcha Nhật Bản, sữa tươi, trân châu trắng",
                            IsAvailable = true,
                            Name = "Trà Sữa Matcha",
                            Price = 40000m,
                            SizeOptions = "M,L",
                            SweetnessOptions = "0%,25%,50%,75%,100%",
                            TemperatureOptions = "Cold,Hot"
                        },
                        new
                        {
                            ProductId = 3,
                            CategoryId = 1,
                            Description = "Trà sữa khoai môn tím thơm béo",
                            ImageUrl = "https://images.unsplash.com/photo-1571934811356-5cc061b6821f?w=400&h=400&fit=crop&crop=center",
                            Ingredients = "Khoai môn tím, sữa tươi, trân châu đen",
                            IsAvailable = true,
                            Name = "Trà Sữa Taro",
                            Price = 38000m,
                            SizeOptions = "M,L",
                            SweetnessOptions = "0%,25%,50%,75%,100%",
                            TemperatureOptions = "Cold,Hot"
                        },
                        new
                        {
                            ProductId = 4,
                            CategoryId = 2,
                            Description = "Cà phê đen truyền thống Việt Nam",
                            ImageUrl = "https://images.unsplash.com/photo-1514432324607-a09d9b4aefdd?w=400&h=400&fit=crop&crop=center",
                            Ingredients = "Cà phê robusta rang xay",
                            IsAvailable = true,
                            Name = "Cà Phê Đen Đá",
                            Price = 25000m,
                            SizeOptions = "M,L",
                            SweetnessOptions = "0%,25%,50%,75%,100%",
                            TemperatureOptions = "Cold,Hot"
                        },
                        new
                        {
                            ProductId = 5,
                            CategoryId = 2,
                            Description = "Cà phê sữa đá đậm đà",
                            ImageUrl = "/images/ca-phe-sua-da.svg",
                            Ingredients = "Cà phê robusta, sữa đặc",
                            IsAvailable = true,
                            Name = "Cà Phê Sữa Đá",
                            Price = 30000m,
                            SizeOptions = "M,L",
                            SweetnessOptions = "0%,25%,50%,75%,100%",
                            TemperatureOptions = "Cold,Hot"
                        },
                        new
                        {
                            ProductId = 6,
                            CategoryId = 2,
                            Description = "Cappuccino Ý thơm ngon",
                            ImageUrl = "https://images.unsplash.com/photo-1534778101976-62847782c213?w=400&h=400&fit=crop&crop=center",
                            Ingredients = "Espresso, sữa tươi, bọt sữa",
                            IsAvailable = true,
                            Name = "Cappuccino",
                            Price = 45000m,
                            SizeOptions = "M,L",
                            SweetnessOptions = "0%,25%,50%,75%,100%",
                            TemperatureOptions = "Hot"
                        },
                        new
                        {
                            ProductId = 7,
                            CategoryId = 3,
                            Description = "Trà đào tươi mát",
                            ImageUrl = "/images/tra-dao.svg",
                            Ingredients = "Trà xanh, đào tươi, mật ong",
                            IsAvailable = true,
                            Name = "Trà Đào",
                            Price = 32000m,
                            SizeOptions = "M,L",
                            SweetnessOptions = "0%,25%,50%,75%,100%",
                            TemperatureOptions = "Cold,Iced"
                        },
                        new
                        {
                            ProductId = 8,
                            CategoryId = 3,
                            Description = "Trà chanh chua ngọt",
                            ImageUrl = "https://images.unsplash.com/photo-1556679343-c7306c1976bc?w=400&h=400&fit=crop&crop=center",
                            Ingredients = "Trà đen, chanh tươi, đường",
                            IsAvailable = true,
                            Name = "Trà Chanh",
                            Price = 28000m,
                            SizeOptions = "M,L",
                            SweetnessOptions = "0%,25%,50%,75%,100%",
                            TemperatureOptions = "Cold,Iced"
                        },
                        new
                        {
                            ProductId = 9,
                            CategoryId = 4,
                            Description = "Sinh tố xoài tươi ngon",
                            ImageUrl = "/images/sinh-to-xoai.svg",
                            Ingredients = "Xoài tươi, sữa tươi, đá",
                            IsAvailable = true,
                            Name = "Sinh Tố Xoài",
                            Price = 42000m,
                            SizeOptions = "M,L",
                            SweetnessOptions = "0%,25%,50%,75%,100%",
                            TemperatureOptions = "Cold"
                        },
                        new
                        {
                            ProductId = 10,
                            CategoryId = 4,
                            Description = "Sinh tố dâu tây tươi mát",
                            ImageUrl = "https://images.unsplash.com/photo-1553530666-ba11a7da3888?w=400&h=400&fit=crop&crop=center",
                            Ingredients = "Dâu tây tươi, sữa tươi, đá",
                            IsAvailable = true,
                            Name = "Sinh Tố Dâu",
                            Price = 45000m,
                            SizeOptions = "M,L",
                            SweetnessOptions = "0%,25%,50%,75%,100%",
                            TemperatureOptions = "Cold"
                        });
                });

            modelBuilder.Entity("BubbleTeaCafe.Models.OrderItem", b =>
                {
                    b.HasOne("BubbleTeaCafe.Models.Order", "Order")
                        .WithMany("OrderItems")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BubbleTeaCafe.Models.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Order");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("BubbleTeaCafe.Models.Product", b =>
                {
                    b.HasOne("BubbleTeaCafe.Models.Category", "Category")
                        .WithMany("Products")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Category");
                });

            modelBuilder.Entity("BubbleTeaCafe.Models.Category", b =>
                {
                    b.Navigation("Products");
                });

            modelBuilder.Entity("BubbleTeaCafe.Models.Order", b =>
                {
                    b.Navigation("OrderItems");
                });
#pragma warning restore 612, 618
        }
    }
}
