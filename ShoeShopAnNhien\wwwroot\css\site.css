/* Base Styles */
html {
  font-size: 14px;
  position: relative;
  min-height: 100%;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
}

/* Focus Styles */
.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
  text-align: start;
}

/* Header Styles */
.top-bar {
  font-size: 0.875rem;
}

.navbar-brand {
  font-size: 1.5rem;
  font-weight: 700;
}

.navbar-nav .nav-link {
  font-weight: 500;
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
  color: #0d6efd !important;
  transform: translateY(-1px);
}

/* Hero Banner */
.hero-banner {
  margin-bottom: 0;
}

.hero-slide {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

/* Category Cards */
.category-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  padding: 1rem;
  border-radius: 10px;
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.category-image img {
  transition: transform 0.3s ease;
  border: 3px solid #f8f9fa;
}

.category-card:hover .category-image img {
  transform: scale(1.05);
  border-color: #0d6efd;
}

/* Product Cards */
.product-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: none;
  border-radius: 15px;
  overflow: hidden;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.product-card .card-img-top {
  transition: transform 0.3s ease;
}

.product-card:hover .card-img-top {
  transform: scale(1.05);
}

.price-section {
  font-size: 1.1rem;
}

/* Buttons */
.btn {
  border-radius: 25px;
  font-weight: 500;
  padding: 0.5rem 1.5rem;
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Footer */
footer {
  margin-top: auto;
}

footer a:hover {
  color: #fff !important;
  text-decoration: underline !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-slide {
    height: 400px !important;
  }

  .hero-slide h1 {
    font-size: 2rem !important;
  }

  .category-card {
    margin-bottom: 2rem;
  }

  .navbar-brand {
    font-size: 1.25rem;
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}