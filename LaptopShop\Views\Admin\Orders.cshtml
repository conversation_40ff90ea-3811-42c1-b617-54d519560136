@model List<LaptopShop.Models.Order>
@{
    ViewData["Title"] = "Quản Lý Đơn Hàng";
}

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 admin-sidebar">
            <div class="d-flex flex-column p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-laptop me-2"></i>LaptopShop Admin
                </h5>
                
                <ul class="nav nav-pills flex-column">
                    <li class="nav-item">
                        <a class="nav-link" asp-action="Index">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-action="Laptops">
                            <i class="fas fa-laptop me-2"></i>Quản lý Laptop
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" asp-action="Orders">
                            <i class="fas fa-shopping-cart me-2"></i>Quản lý Đơn hàng
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-action="Customers">
                            <i class="fas fa-users me-2"></i>Quản lý Khách hàng
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-action="Categories">
                            <i class="fas fa-tags me-2"></i>Danh mục
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-action="Brands">
                            <i class="fas fa-trademark me-2"></i>Thương hiệu
                        </a>
                    </li>
                </ul>

                <hr class="text-white">
                
                <div class="mt-auto">
                    <a class="nav-link text-light" asp-controller="Home" asp-action="Index" target="_blank">
                        <i class="fas fa-external-link-alt me-2"></i>Xem Website
                    </a>
                    <a class="nav-link text-light" asp-action="Logout">
                        <i class="fas fa-sign-out-alt me-2"></i>Đăng xuất
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <div class="p-4">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="gradient-text">Quản Lý Đơn Hàng</h2>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary">
                            <i class="fas fa-download me-2"></i>Xuất Excel
                        </button>
                        <button class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Tạo Đơn Hàng
                        </button>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-shopping-cart fa-2x text-primary mb-2"></i>
                                <h4 class="text-primary">@(Model?.Count ?? 0)</h4>
                                <p class="text-muted mb-0">Tổng đơn hàng</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                                <h4 class="text-warning">@(Model?.Count(o => o.Status == LaptopShop.Models.OrderStatus.Pending) ?? 0)</h4>
                                <p class="text-muted mb-0">Chờ xử lý</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-truck fa-2x text-info mb-2"></i>
                                <h4 class="text-info">@(Model?.Count(o => o.Status == LaptopShop.Models.OrderStatus.Shipping) ?? 0)</h4>
                                <p class="text-muted mb-0">Đang giao</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                <h4 class="text-success">@(Model?.Count(o => o.Status == LaptopShop.Models.OrderStatus.Delivered) ?? 0)</h4>
                                <p class="text-muted mb-0">Hoàn thành</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Search & Filter -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="Tìm kiếm đơn hàng...">
                            <button class="btn btn-outline-secondary" type="button">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select">
                            <option value="">Tất cả trạng thái</option>
                            <option value="Pending">Chờ xử lý</option>
                            <option value="Processing">Đang xử lý</option>
                            <option value="Shipping">Đang giao</option>
                            <option value="Delivered">Hoàn thành</option>
                            <option value="Cancelled">Đã hủy</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <input type="date" class="form-control" placeholder="Từ ngày">
                    </div>
                    <div class="col-md-2">
                        <input type="date" class="form-control" placeholder="Đến ngày">
                    </div>
                </div>

                <!-- Orders Table -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Mã đơn</th>
                                        <th>Khách hàng</th>
                                        <th>Ngày đặt</th>
                                        <th>Sản phẩm</th>
                                        <th>Tổng tiền</th>
                                        <th>Trạng thái</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (Model != null && Model.Any())
                                    {
                                        @foreach (var order in Model)
                                        {
                                            <tr>
                                                <td>
                                                    <strong>#@order.OrderNumber</strong>
                                                </td>
                                                <td>
                                                    <div>
                                                        <strong>@order.CustomerName</strong>
                                                        <br>
                                                        <small class="text-muted">@order.CustomerEmail</small>
                                                    </div>
                                                </td>
                                                <td>@order.CreatedAt.ToString("dd/MM/yyyy HH:mm")</td>
                                                <td>
                                                    <span class="badge bg-info">@order.OrderDetails.Count sản phẩm</span>
                                                </td>
                                                <td>
                                                    <strong>@order.TotalAmount.ToString("N0") ₫</strong>
                                                </td>
                                                <td>
                                                    @{
                                                        var statusClass = order.Status switch
                                                        {
                                                            LaptopShop.Models.OrderStatus.Pending => "bg-warning",
                                                            LaptopShop.Models.OrderStatus.Processing => "bg-info",
                                                            LaptopShop.Models.OrderStatus.Shipping => "bg-primary",
                                                            LaptopShop.Models.OrderStatus.Delivered => "bg-success",
                                                            LaptopShop.Models.OrderStatus.Cancelled => "bg-danger",
                                                            _ => "bg-secondary"
                                                        };
                                                        
                                                        var statusText = order.Status switch
                                                        {
                                                            LaptopShop.Models.OrderStatus.Pending => "Chờ xử lý",
                                                            LaptopShop.Models.OrderStatus.Processing => "Đang xử lý",
                                                            LaptopShop.Models.OrderStatus.Shipping => "Đang giao",
                                                            LaptopShop.Models.OrderStatus.Delivered => "Hoàn thành",
                                                            LaptopShop.Models.OrderStatus.Cancelled => "Đã hủy",
                                                            _ => "Không xác định"
                                                        };
                                                    }
                                                    <span class="badge @statusClass">@statusText</span>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Xem chi tiết">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <button class="btn btn-sm btn-outline-warning" title="Cập nhật trạng thái">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-info" title="In hóa đơn">
                                                            <i class="fas fa-print"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                    }
                                    else
                                    {
                                        <tr>
                                            <td colspan="7" class="text-center py-4">
                                                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                                <p class="text-muted">Chưa có đơn hàng nào trong hệ thống</p>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                @if (Model != null && Model.Any())
                {
                    <nav aria-label="Order pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1">Trước</a>
                            </li>
                            <li class="page-item active">
                                <a class="page-link" href="#">1</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">2</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">3</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">Sau</a>
                            </li>
                        </ul>
                    </nav>
                }
            </div>
        </div>
    </div>
</div>
