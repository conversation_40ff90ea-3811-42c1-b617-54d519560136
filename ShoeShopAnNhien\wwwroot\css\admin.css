/* Admin Panel Styles */

.admin-body {
    background-color: #f8f9fa;
    padding-top: 56px; /* Height of fixed navbar */
}

.admin-wrapper {
    display: flex;
    min-height: calc(100vh - 56px);
}

/* Sidebar */
.sidebar {
    width: 250px;
    min-height: calc(100vh - 56px);
    position: fixed;
    top: 56px;
    left: 0;
    z-index: 1000;
    border-right: 1px solid #dee2e6;
    overflow-y: auto;
}

.sidebar-content {
    padding: 1rem 0;
}

.sidebar .nav-link {
    color: #495057;
    padding: 0.75rem 1.5rem;
    border-radius: 0;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    background-color: #e9ecef;
    color: #0d6efd;
}

.sidebar .nav-link.active {
    background-color: #0d6efd;
    color: white;
}

.sidebar .nav-link i {
    width: 20px;
}

/* Main Content */
.main-content {
    margin-left: 250px;
    flex: 1;
    padding: 0;
}

/* Dashboard Cards */
.dashboard-card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-2px);
}

.dashboard-card .card-body {
    padding: 1.5rem;
}

.dashboard-card .card-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.dashboard-card.bg-primary .card-icon {
    color: rgba(255,255,255,0.8);
}

.dashboard-card.bg-success .card-icon {
    color: rgba(255,255,255,0.8);
}

.dashboard-card.bg-warning .card-icon {
    color: rgba(255,255,255,0.8);
}

.dashboard-card.bg-info .card-icon {
    color: rgba(255,255,255,0.8);
}

/* Tables */
.admin-table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.admin-table .table {
    margin-bottom: 0;
}

.admin-table .table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.admin-table .table td {
    vertical-align: middle;
}

/* Status Badges */
.status-badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 50px;
}

/* Action Buttons */
.action-buttons .btn {
    margin-right: 0.25rem;
    margin-bottom: 0.25rem;
}

/* Stats Cards */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.stats-card h3 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stats-card p {
    margin-bottom: 0;
    opacity: 0.9;
}

/* Product Images */
.product-image {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 5px;
}

/* Order Status Colors */
.order-status-pending { background-color: #ffc107; }
.order-status-confirmed { background-color: #17a2b8; }
.order-status-processing { background-color: #007bff; }
.order-status-shipping { background-color: #6c757d; }
.order-status-delivered { background-color: #28a745; }
.order-status-cancelled { background-color: #dc3545; }
.order-status-returned { background-color: #343a40; }

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .dashboard-card {
        margin-bottom: 1rem;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0d6efd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom Scrollbar for Sidebar */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.sidebar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Page Headers */
.page-header {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.page-header h1 {
    margin-bottom: 0.5rem;
    color: #495057;
}

.page-header .breadcrumb {
    margin-bottom: 0;
    background: none;
    padding: 0;
}

/* Form Styles */
.admin-form {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.admin-form .form-label {
    font-weight: 600;
    color: #495057;
}

/* Chart Containers */
.chart-container {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.chart-container h5 {
    margin-bottom: 1rem;
    color: #495057;
    font-weight: 600;
}
