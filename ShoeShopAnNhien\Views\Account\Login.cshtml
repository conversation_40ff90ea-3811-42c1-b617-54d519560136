@model ShoeShopAnNhien.ViewModels.LoginViewModel
@{
    ViewData["Title"] = "Đăng Nhập";
}

<!-- Hero Section -->
<section class="hero-section bg-primary text-white py-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-5 fw-bold mb-2">Đ<PERSON>ng N<PERSON>p</h1>
                <p class="lead mb-0">Chào mừng bạn quay trở lại</p>
            </div>
            <div class="col-lg-4 text-end">
                <i class="fas fa-sign-in-alt" style="font-size: 4rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
</section>

<!-- Login Form -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7">
                <div class="card border-0 shadow-lg">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <h3 class="fw-bold">Đăng Nhập Tài Khoản</h3>
                            <p class="text-muted">Nhập thông tin để đăng nhập</p>
                        </div>

                        <form asp-action="Login" method="post">
                            <div asp-validation-summary="All" class="text-danger mb-3"></div>
                            
                            <div class="mb-3">
                                <label asp-for="Email" class="form-label"></label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                    <input asp-for="Email" class="form-control" placeholder="Nhập email" autofocus>
                                </div>
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>

                            <div class="mb-3">
                                <label asp-for="Password" class="form-label"></label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input asp-for="Password" class="form-control" placeholder="Nhập mật khẩu">
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('Password')">
                                        <i class="fas fa-eye" id="togglePasswordIcon"></i>
                                    </button>
                                </div>
                                <span asp-validation-for="Password" class="text-danger"></span>
                            </div>

                            <div class="mb-3 d-flex justify-content-between align-items-center">
                                <div class="form-check">
                                    <input asp-for="RememberMe" class="form-check-input">
                                    <label asp-for="RememberMe" class="form-check-label"></label>
                                </div>
                                <a href="#" class="text-decoration-none small">Quên mật khẩu?</a>
                            </div>

                            <button type="submit" class="btn btn-primary w-100 btn-lg mb-3">
                                <i class="fas fa-sign-in-alt me-2"></i>Đăng Nhập
                            </button>

                            <div class="text-center">
                                <p class="mb-0">Chưa có tài khoản? 
                                    <a asp-action="Register" asp-route-returnUrl="@ViewData["ReturnUrl"]" class="text-decoration-none fw-bold">Đăng ký ngay</a>
                                </p>
                            </div>
                        </form>

                        <!-- Demo Account Info -->
                        <div class="mt-4 p-3 bg-light rounded">
                            <h6 class="fw-bold mb-2">Tài Khoản Demo:</h6>
                            <small class="text-muted">
                                <strong>Admin:</strong> <EMAIL> / Admin123!<br>
                                <strong>Hoặc tạo tài khoản mới để trải nghiệm</strong>
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Social Login (Optional) -->
                <div class="card border-0 shadow-sm mt-4">
                    <div class="card-body p-4">
                        <div class="text-center mb-3">
                            <small class="text-muted">Hoặc đăng nhập bằng</small>
                        </div>
                        <div class="row g-2">
                            <div class="col-6">
                                <button class="btn btn-outline-primary w-100" type="button">
                                    <i class="fab fa-facebook-f me-2"></i>Facebook
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-outline-danger w-100" type="button">
                                    <i class="fab fa-google me-2"></i>Google
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Benefits Sidebar -->
            <div class="col-lg-4 col-md-5 mt-4 mt-lg-0">
                <div class="ps-lg-4">
                    <h5 class="fw-bold mb-4">Lợi Ích Thành Viên</h5>
                    
                    <div class="benefit-item d-flex mb-3">
                        <div class="benefit-icon me-3">
                            <i class="fas fa-star text-warning fs-4"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">Tích Điểm Thưởng</h6>
                            <small class="text-muted">Tích điểm mỗi lần mua hàng và đổi quà hấp dẫn</small>
                        </div>
                    </div>

                    <div class="benefit-item d-flex mb-3">
                        <div class="benefit-icon me-3">
                            <i class="fas fa-gift text-success fs-4"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">Ưu Đãi Độc Quyền</h6>
                            <small class="text-muted">Nhận thông báo về các chương trình khuyến mãi đặc biệt</small>
                        </div>
                    </div>

                    <div class="benefit-item d-flex mb-3">
                        <div class="benefit-icon me-3">
                            <i class="fas fa-history text-info fs-4"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">Lịch Sử Mua Hàng</h6>
                            <small class="text-muted">Theo dõi đơn hàng và lịch sử mua sắm dễ dàng</small>
                        </div>
                    </div>

                    <div class="benefit-item d-flex mb-3">
                        <div class="benefit-icon me-3">
                            <i class="fas fa-heart text-danger fs-4"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">Danh Sách Yêu Thích</h6>
                            <small class="text-muted">Lưu các sản phẩm yêu thích để mua sau</small>
                        </div>
                    </div>

                    <div class="benefit-item d-flex">
                        <div class="benefit-icon me-3">
                            <i class="fas fa-headset text-primary fs-4"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">Hỗ Trợ Ưu Tiên</h6>
                            <small class="text-muted">Được hỗ trợ ưu tiên từ đội ngũ chăm sóc khách hàng</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById('toggle' + fieldId + 'Icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}
</script>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
