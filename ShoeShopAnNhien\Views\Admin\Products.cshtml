@{
    ViewData["Title"] = "<PERSON><PERSON><PERSON><PERSON> <PERSON>ẩm - Admin Panel";
    Layout = "_AdminLayout";

    // Mock data for demonstration
    var mockProducts = new List<dynamic>
    {
        new { Id = 1, Name = "Nike Air Max 270", SKU = "NK-AM270-001", Category = "<PERSON><PERSON><PERSON><PERSON>hao", Brand = "Nike", Price = 2200000, SalePrice = 1980000, Stock = 45, IsActive = true, IsFeatured = true, Image = "https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/99486859-0ff3-46b4-949b-2d16af2ad421/custom-nike-air-max-90.png" },
        new { Id = 2, Name = "Adidas Ultraboost 22", SKU = "AD-UB22-002", Category = "Giày Thể Thao", Brand = "Adidas", Price = 3200000, SalePrice = (decimal?)null, Stock = 23, IsActive = true, IsFeatured = true, Image = "https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/fbaf991a78bc4896a3e9ad7800abcec6_9366/Ultraboost_22_Shoes_Black_GZ0127_01_standard.jpg" },
        new { Id = 3, Name = "Converse Chuck Taylor All Star", SKU = "CV-CT-003", Category = "Giày Sneaker", Brand = "Converse", Price = 990000, SalePrice = 792000, Stock = 67, IsActive = true, IsFeatured = false, Image = "https://www.converse.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-cnv-master-catalog/default/dw2f8b0b3e/images/a_107/M9160_A_107X1.jpg" },
        new { Id = 4, Name = "Vans Old Skool", SKU = "VN-OS-004", Category = "Giày Sneaker", Brand = "Vans", Price = 1650000, SalePrice = (decimal?)null, Stock = 34, IsActive = true, IsFeatured = true, Image = "https://images.vans.com/is/image/Vans/D3HY28-HERO?$583x583$" },
        new { Id = 5, Name = "Biti's Hunter Street", SKU = "BT-HS-005", Category = "Giày Thể Thao", Brand = "Biti's", Price = 520000, SalePrice = 416000, Stock = 89, IsActive = true, IsFeatured = false, Image = "https://product.hstatic.net/1000230642/product/dsmh00700den__6__beaeb93d4e8e4b9e9b7c0c7c5c5c5c5c.jpg" },
        new { Id = 6, Name = "Giày Tây Oxford Classic", SKU = "OX-CL-006", Category = "Giày Tây", Brand = "Classic", Price = 1980000, SalePrice = (decimal?)null, Stock = 12, IsActive = true, IsFeatured = false, Image = "https://product.hstatic.net/200000265619/product/giay-tay-nam-oxford-da-bo-that-cao-cap-od15-1_grande.jpg" },
        new { Id = 7, Name = "Giày Cao Gót Nữ Elegant", SKU = "EL-HH-007", Category = "Giày Cao Gót", Brand = "Elegant", Price = 1450000, SalePrice = 1160000, Stock = 28, IsActive = true, IsFeatured = true, Image = "https://product.hstatic.net/1000230642/product/giay-cao-got-nu-dep-2021-cg001_grande.jpg" },
        new { Id = 8, Name = "Sandal Nữ Summer", SKU = "SM-SD-008", Category = "Sandal", Brand = "Summer", Price = 680000, SalePrice = (decimal?)null, Stock = 56, IsActive = false, IsFeatured = false, Image = "https://product.hstatic.net/1000230642/product/sandal-nu-dep-2021-sd001_grande.jpg" },
        new { Id = 9, Name = "Boot Nam Leather", SKU = "LT-BT-009", Category = "Boot", Brand = "Leather", Price = 2800000, SalePrice = 2520000, Stock = 8, IsActive = true, IsFeatured = false, Image = "https://product.hstatic.net/1000230642/product/boot-nam-da-that-bt001_grande.jpg" },
        new { Id = 10, Name = "Giày Chạy Bộ Pro Runner", SKU = "PR-RN-010", Category = "Giày Thể Thao", Brand = "Pro Runner", Price = 1850000, SalePrice = (decimal?)null, Stock = 41, IsActive = true, IsFeatured = true, Image = "https://product.hstatic.net/1000230642/product/giay-chay-bo-nam-pr001_grande.jpg" }
    };

    ViewBag.TotalItems = mockProducts.Count;
    ViewBag.CurrentPage = 1;
    ViewBag.TotalPages = 1;
}

<!-- Page Header -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="h3 mb-0">Quản Lý Sản Phẩm</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="@Url.Action("Index", "Admin")">Dashboard</a></li>
                    <li class="breadcrumb-item active">Sản Phẩm</li>
                </ol>
            </nav>
        </div>
        <div>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addProductModal">
                <i class="fas fa-plus me-2"></i>Thêm Sản Phẩm
            </button>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <input type="text" class="form-control" id="searchProducts" placeholder="Tìm kiếm sản phẩm..." data-table-search="productsTable">
            </div>
            <div class="col-md-2">
                <select class="form-select" id="filterCategory">
                    <option value="">Tất cả danh mục</option>
                    <!-- Categories will be populated here -->
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="filterBrand">
                    <option value="">Tất cả thương hiệu</option>
                    <!-- Brands will be populated here -->
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="filterStatus">
                    <option value="">Tất cả trạng thái</option>
                    <option value="active">Hoạt động</option>
                    <option value="inactive">Tạm dừng</option>
                </select>
            </div>
            <div class="col-md-3">
                <div class="btn-group" role="group">
                    <button class="btn btn-outline-secondary" onclick="exportToCSV('productsTable', 'products.csv')">
                        <i class="fas fa-download me-1"></i>Xuất CSV
                    </button>
                    <button class="btn btn-outline-secondary" onclick="printTable('productsTable')">
                        <i class="fas fa-print me-1"></i>In
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Products Table -->
<div class="card admin-table">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Danh Sách Sản Phẩm (@ViewBag.TotalItems sản phẩm)</h5>
        <div class="d-flex align-items-center">
            <span class="text-muted me-3">Trang @ViewBag.CurrentPage / @ViewBag.TotalPages</span>
        </div>
    </div>
    <div class="card-body p-0">
        @if (mockProducts.Any())
        {
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="productsTable">
                    <thead>
                        <tr>
                            <th>Hình Ảnh</th>
                            <th>Tên Sản Phẩm</th>
                            <th>SKU</th>
                            <th>Danh Mục</th>
                            <th>Thương Hiệu</th>
                            <th>Giá</th>
                            <th>Tồn Kho</th>
                            <th>Trạng Thái</th>
                            <th>Thao Tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var product in mockProducts)
                        {
                            <tr>
                                <td>
                                    <img src="@product.Image"
                                         alt="@product.Name"
                                         class="product-image"
                                         style="width: 50px; height: 50px; object-fit: cover; border-radius: 5px;"
                                         onerror="this.src='https://via.placeholder.com/50x50/e9ecef/6c757d?text=No+Image'">
                                </td>
                                <td>
                                    <div>
                                        <h6 class="mb-1">@product.Name</h6>
                                        @if (product.IsFeatured)
                                        {
                                            <span class="badge bg-warning text-dark">Nổi bật</span>
                                        }
                                    </div>
                                </td>
                                <td>
                                    <code>@product.SKU</code>
                                </td>
                                <td>@product.Category</td>
                                <td>@product.Brand</td>
                                <td>
                                    @if (product.SalePrice != null)
                                    {
                                        <span class="text-danger fw-bold">@product.SalePrice?.ToString("N0") VNĐ</span>
                                        <br><small class="text-muted text-decoration-line-through">@product.Price.ToString("N0") VNĐ</small>
                                    }
                                    else
                                    {
                                        <span class="fw-bold">@product.Price.ToString("N0") VNĐ</span>
                                    }
                                </td>
                                <td>
                                    <span class="badge @(product.Stock <= 10 ? "bg-danger" : product.Stock <= 20 ? "bg-warning" : "bg-success")">
                                        @product.Stock
                                    </span>
                                </td>
                                <td>
                                    <span class="badge @(product.IsActive ? "bg-success" : "bg-secondary")">
                                        @(product.IsActive ? "Hoạt động" : "Tạm dừng")
                                    </span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-outline-primary btn-sm" data-bs-toggle="tooltip" title="Xem chi tiết">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-warning btn-sm" data-bs-toggle="tooltip" title="Chỉnh sửa">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-@(product.IsActive ? "secondary" : "success") btn-sm"
                                                onclick="toggleProductStatus(@product.Id)"
                                                data-bs-toggle="tooltip"
                                                title="@(product.IsActive ? "Tạm dừng" : "Kích hoạt")">
                                            <i class="fas fa-@(product.IsActive ? "pause" : "play")"></i>
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm" data-bs-toggle="tooltip" title="Xóa">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-5">
                <i class="fas fa-box text-muted" style="font-size: 4rem; opacity: 0.3;"></i>
                <h5 class="mt-3 text-muted">Chưa có sản phẩm nào</h5>
                <p class="text-muted">Hãy thêm sản phẩm đầu tiên cho cửa hàng</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addProductModal">
                    <i class="fas fa-plus me-2"></i>Thêm Sản Phẩm
                </button>
            </div>
        }
    </div>
</div>

<!-- Pagination -->
@if (ViewBag.TotalPages > 1)
{
    <nav aria-label="Products pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            @if (ViewBag.CurrentPage > 1)
            {
                <li class="page-item">
                    <a class="page-link" href="@Url.Action("Products", new { page = ViewBag.CurrentPage - 1 })">
                        <i class="fas fa-chevron-left"></i> Trước
                    </a>
                </li>
            }

            @for (int i = Math.Max(1, ViewBag.CurrentPage - 2); i <= Math.Min(ViewBag.TotalPages, ViewBag.CurrentPage + 2); i++)
            {
                <li class="page-item @(i == ViewBag.CurrentPage ? "active" : "")">
                    <a class="page-link" href="@Url.Action("Products", new { page = i })">@i</a>
                </li>
            }

            @if (ViewBag.CurrentPage < ViewBag.TotalPages)
            {
                <li class="page-item">
                    <a class="page-link" href="@Url.Action("Products", new { page = ViewBag.CurrentPage + 1 })">
                        Sau <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            }
        </ul>
    </nav>
}

<!-- Add Product Modal -->
<div class="modal fade" id="addProductModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Thêm Sản Phẩm Mới</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addProductForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Tên Sản Phẩm *</label>
                            <input type="text" class="form-control" name="Name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">SKU</label>
                            <input type="text" class="form-control" name="SKU">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Mô Tả *</label>
                        <textarea class="form-control" name="Description" rows="3" required></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Giá *</label>
                            <input type="number" class="form-control" name="Price" min="0" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Giá Khuyến Mãi</label>
                            <input type="number" class="form-control" name="SalePrice" min="0">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Số Lượng *</label>
                            <input type="number" class="form-control" name="StockQuantity" min="0" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Danh Mục *</label>
                            <select class="form-select" name="CategoryId" required>
                                <option value="">Chọn danh mục</option>
                                <!-- Categories will be populated here -->
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Thương Hiệu *</label>
                            <select class="form-select" name="BrandId" required>
                                <option value="">Chọn thương hiệu</option>
                                <!-- Brands will be populated here -->
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Sizes Có Sẵn</label>
                            <input type="text" class="form-control" name="AvailableSizes" placeholder="36,37,38,39,40,41,42">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Màu Sắc Có Sẵn</label>
                            <input type="text" class="form-control" name="AvailableColors" placeholder="Đen,Trắng,Xanh">
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="IsFeatured" id="isFeatured">
                            <label class="form-check-label" for="isFeatured">
                                Sản phẩm nổi bật
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" onclick="saveProduct()">
                    <i class="fas fa-save me-2"></i>Lưu Sản Phẩm
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function saveProduct() {
    // This would normally submit to server
    showToast('Chức năng thêm sản phẩm sẽ được phát triển trong phiên bản tiếp theo!', 'info');
}
</script>
