#include "Student.h"
#include <algorithm>
#include <cctype>

// Constructor mặc định
Student::Student() : studentId(""), fullName(""), averageScore(0.0), birthYear(2000) {}

// Constructor có tham số
Student::Student(const std::string& id, const std::string& name, double score, int year)
    : studentId(id), fullName(name), averageScore(score), birthYear(year) {
    if (!isValidScore(score)) {
        averageScore = 0.0;
    }
    if (!isValidYear(year)) {
        birthYear = 2000;
    }
}

// Copy constructor
Student::Student(const Student& other)
    : studentId(other.studentId), fullName(other.fullName), 
      averageScore(other.averageScore), birthYear(other.birthYear) {}

// Assignment operator
Student& Student::operator=(const Student& other) {
    if (this != &other) {
        studentId = other.studentId;
        fullName = other.fullName;
        averageScore = other.averageScore;
        birthYear = other.birthYear;
    }
    return *this;
}

// Destructor
Student::~Student() {}

// Getter methods
std::string Student::getStudentId() const { return studentId; }
std::string Student::getFullName() const { return fullName; }
double Student::getAverageScore() const { return averageScore; }
int Student::getBirthYear() const { return birthYear; }

// Setter methods
void Student::setStudentId(const std::string& id) {
    if (isValidId(id)) {
        studentId = id;
    }
}

void Student::setFullName(const std::string& name) {
    fullName = name;
}

void Student::setAverageScore(double score) {
    if (isValidScore(score)) {
        averageScore = score;
    }
}

void Student::setBirthYear(int year) {
    if (isValidYear(year)) {
        birthYear = year;
    }
}

// Comparison operators (mặc định so sánh theo điểm trung bình)
bool Student::operator<(const Student& other) const {
    return averageScore < other.averageScore;
}

bool Student::operator>(const Student& other) const {
    return averageScore > other.averageScore;
}

bool Student::operator<=(const Student& other) const {
    return averageScore <= other.averageScore;
}

bool Student::operator>=(const Student& other) const {
    return averageScore >= other.averageScore;
}

bool Student::operator==(const Student& other) const {
    return studentId == other.studentId;
}

bool Student::operator!=(const Student& other) const {
    return studentId != other.studentId;
}

// Input operator
std::istream& operator>>(std::istream& is, Student& student) {
    std::cout << "Nhap ma sinh vien: ";
    std::getline(is, student.studentId);
    
    std::cout << "Nhap ho va ten: ";
    std::getline(is, student.fullName);
    
    std::cout << "Nhap diem trung binh (0-10): ";
    is >> student.averageScore;
    
    std::cout << "Nhap nam sinh: ";
    is >> student.birthYear;
    
    is.ignore(); // Ignore newline character
    
    return is;
}

// Output operator
std::ostream& operator<<(std::ostream& os, const Student& student) {
    os << std::left << std::setw(12) << student.studentId
       << std::setw(25) << student.fullName
       << std::setw(8) << std::fixed << std::setprecision(2) << student.averageScore
       << std::setw(10) << student.birthYear;
    return os;
}

// Display methods
void Student::display() const {
    std::cout << *this << std::endl;
}

void Student::displayHeader() const {
    std::cout << std::left << std::setw(12) << "Ma SV"
              << std::setw(25) << "Ho va Ten"
              << std::setw(8) << "Diem TB"
              << std::setw(10) << "Nam sinh" << std::endl;
    std::cout << std::string(55, '-') << std::endl;
}

// Validation methods
bool Student::isValidScore(double score) const {
    return score >= 0.0 && score <= 10.0;
}

bool Student::isValidYear(int year) const {
    return year >= 1900 && year <= 2010;
}

bool Student::isValidId(const std::string& id) const {
    return !id.empty() && id.length() <= 15;
}

// Comparison functions for different sorting criteria
bool compareById(const Student& a, const Student& b) {
    return a.getStudentId() < b.getStudentId();
}

bool compareByName(const Student& a, const Student& b) {
    return a.getFullName() < b.getFullName();
}

bool compareByScore(const Student& a, const Student& b) {
    return a.getAverageScore() > b.getAverageScore(); // Sắp xếp giảm dần theo điểm
}

bool compareByYear(const Student& a, const Student& b) {
    return a.getBirthYear() < b.getBirthYear();
}
