﻿<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Shop Giày An Nhiên</title>
    <meta name="description" content="Shop Giày An Nhiên - Chuyên cung cấp giày thể thao, giày cao gót, giày tây chính hãng với giá tốt nhất" />
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/ShoeShopAnNhien.styles.css" asp-append-version="true" />
</head>
<body>
    <header>
        <!-- Top Bar -->
        <div class="top-bar bg-dark text-white py-2">
            <div class="container">
                <div class="row">
                    <div class="col-md-6">
                        <small><i class="fas fa-phone"></i> Hotline: 0123.456.789 | <i class="fas fa-envelope"></i> <EMAIL></small>
                    </div>
                    <div class="col-md-6 text-end">
                        <small>
                            <a href="#" class="text-white text-decoration-none me-3"><i class="fab fa-facebook"></i></a>
                            <a href="#" class="text-white text-decoration-none me-3"><i class="fab fa-instagram"></i></a>
                            <a href="#" class="text-white text-decoration-none"><i class="fab fa-youtube"></i></a>
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Navigation -->
        <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
            <div class="container">
                <a class="navbar-brand fw-bold text-primary" asp-area="" asp-controller="Home" asp-action="Index">
                    <i class="fas fa-shoe-prints"></i> Shop Giày An Nhiên
                </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Home" asp-action="Index">
                                <i class="fas fa-home"></i> Trang Chủ
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-th-large"></i> Sản Phẩm
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" asp-controller="Products" asp-action="Index">Tất Cả Sản Phẩm</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" asp-controller="Products" asp-action="Category" asp-route-id="1">Giày Thể Thao</a></li>
                                <li><a class="dropdown-item" asp-controller="Products" asp-action="Category" asp-route-id="2">Giày Cao Gót</a></li>
                                <li><a class="dropdown-item" asp-controller="Products" asp-action="Category" asp-route-id="3">Giày Tây</a></li>
                                <li><a class="dropdown-item" asp-controller="Products" asp-action="Category" asp-route-id="4">Giày Sandal</a></li>
                                <li><a class="dropdown-item" asp-controller="Products" asp-action="Category" asp-route-id="5">Giày Boot</a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Home" asp-action="About">
                                <i class="fas fa-info-circle"></i> Giới Thiệu
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Home" asp-action="Contact">
                                <i class="fas fa-phone"></i> Liên Hệ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="News" asp-action="Index">
                                <i class="fas fa-newspaper"></i> Tin Tức
                            </a>
                        </li>
                    </ul>

                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link position-relative" asp-controller="Cart" asp-action="Index">
                                <i class="fas fa-shopping-cart"></i> Giỏ Hàng
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="cart-count">
                                    0
                                </span>
                            </a>
                        </li>
                        @if (User.Identity!.IsAuthenticated)
                        {
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user"></i> @User.Identity.Name
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" asp-controller="Account" asp-action="Profile">
                                        <i class="fas fa-user me-2"></i>Thông Tin Cá Nhân
                                    </a></li>
                                    <li><a class="dropdown-item" asp-controller="Orders" asp-action="MyOrders">
                                        <i class="fas fa-shopping-bag me-2"></i>Đơn Hàng Của Tôi
                                    </a></li>
                                    @if (User.IsInRole("Admin"))
                                    {
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-primary" asp-controller="Admin" asp-action="Index">
                                            <i class="fas fa-cogs me-2"></i>Quản Trị Viên
                                        </a></li>
                                    }
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form asp-controller="Account" asp-action="Logout" method="post" class="d-inline">
                                            <button type="submit" class="dropdown-item text-danger">
                                                <i class="fas fa-sign-out-alt me-2"></i>Đăng Xuất
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        }
                        else
                        {
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user"></i> Tài Khoản
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" asp-controller="Account" asp-action="Login">
                                        <i class="fas fa-sign-in-alt me-2"></i>Đăng Nhập
                                    </a></li>
                                    <li><a class="dropdown-item" asp-controller="Account" asp-action="Register">
                                        <i class="fas fa-user-plus me-2"></i>Đăng Ký
                                    </a></li>
                                </ul>
                            </li>
                        }
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    <main role="main">
        @RenderBody()
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white mt-5">
        <div class="container py-5">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <h5 class="text-primary mb-3">
                        <i class="fas fa-shoe-prints"></i> Shop Giày An Nhiên
                    </h5>
                    <p class="mb-3">Chuyên cung cấp các loại giày thể thao, giày cao gót, giày tây chính hãng với chất lượng tốt nhất và giá cả hợp lý.</p>
                    <div class="d-flex">
                        <a href="#" class="text-white me-3 fs-4"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="text-white me-3 fs-4"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-white me-3 fs-4"><i class="fab fa-youtube"></i></a>
                        <a href="#" class="text-white fs-4"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>

                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="text-uppercase mb-3">Danh Mục</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-white-50 text-decoration-none">Giày Thể Thao</a></li>
                        <li><a href="#" class="text-white-50 text-decoration-none">Giày Cao Gót</a></li>
                        <li><a href="#" class="text-white-50 text-decoration-none">Giày Tây</a></li>
                        <li><a href="#" class="text-white-50 text-decoration-none">Giày Sandal</a></li>
                        <li><a href="#" class="text-white-50 text-decoration-none">Giày Boot</a></li>
                    </ul>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <h6 class="text-uppercase mb-3">Hỗ Trợ</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-white-50 text-decoration-none">Hướng Dẫn Mua Hàng</a></li>
                        <li><a href="#" class="text-white-50 text-decoration-none">Chính Sách Đổi Trả</a></li>
                        <li><a href="#" class="text-white-50 text-decoration-none">Chính Sách Bảo Hành</a></li>
                        <li><a href="#" class="text-white-50 text-decoration-none">Phương Thức Thanh Toán</a></li>
                        <li><a href="#" class="text-white-50 text-decoration-none">Vận Chuyển</a></li>
                    </ul>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <h6 class="text-uppercase mb-3">Liên Hệ</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            123 Đường ABC, Quận XYZ, TP.HCM
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-phone me-2"></i>
                            0123.456.789
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-envelope me-2"></i>
                            <EMAIL>
                        </li>
                        <li>
                            <i class="fas fa-clock me-2"></i>
                            8:00 - 22:00 (Thứ 2 - Chủ Nhật)
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="border-top border-secondary">
            <div class="container py-3">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <small>&copy; 2025 Shop Giày An Nhiên. Tất cả quyền được bảo lưu.</small>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <small>
                            <a href="#" class="text-white-50 text-decoration-none me-3">Chính Sách Bảo Mật</a>
                            <a href="#" class="text-white-50 text-decoration-none">Điều Khoản Sử Dụng</a>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)

    <!-- Anti-forgery token for AJAX requests -->
    <input name="__RequestVerificationToken" type="hidden" value="@Html.AntiForgeryToken().ToString().Replace("<input name=\"__RequestVerificationToken\" type=\"hidden\" value=\"", "").Replace("\" />", "")" />
</body>
</html>
