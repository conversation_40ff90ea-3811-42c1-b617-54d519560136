using Microsoft.AspNetCore.Mvc;
using BubbleTeaCafe.Models;

namespace BubbleTeaCafe.Controllers
{
    public class AccountController : Controller
    {
        public IActionResult Login()
        {
            return View();
        }

        [HttpPost]
        public IActionResult Login(LoginViewModel model)
        {
            if (ModelState.IsValid)
            {
                // Xử lý đăng nhập ở đây
                // Tạm thời redirect về trang chủ
                return RedirectToAction("Index", "Home");
            }
            return View(model);
        }

        public IActionResult Register()
        {
            return View();
        }

        [HttpPost]
        public IActionResult Register(RegisterViewModel model)
        {
            if (ModelState.IsValid)
            {
                // Xử lý đăng ký ở đây
                // Tạm thời redirect về trang đăng nhập
                return RedirectToAction("Login");
            }
            return View(model);
        }

        public IActionResult Logout()
        {
            // Xử lý đăng xuất ở đây
            return RedirectToAction("Index", "Home");
        }
    }
}
