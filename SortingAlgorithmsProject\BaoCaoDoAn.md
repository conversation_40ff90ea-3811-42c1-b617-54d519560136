# BÁO CÁO ĐỒ ÁN
## MÔ PHỎNG CÁC THUẬT TOÁN SẮP XẾP NỘI CƠ BẢN

---

**Sin<PERSON> viên thực hiện:** [Tên sinh viên]  
**Mã số sinh viên:** [MSSV]  
**Lớp:** [Tên lớp]  
**Giảng viên hướng dẫn:** [Tên giảng viên]  
**Họ<PERSON> phần:** C<PERSON>u trúc dữ liệu và Giải thuật  

---

## MỤC LỤC

**Chương 1: Mở đầu**
- 1.1 Giới thiệu đề tài
- 1.2 Mục tiêu đề tài  
- 1.3 B<PERSON> cục báo cáo

**Chương 2: T<PERSON><PERSON> quan về các thuật toán sắp xếp nội**
- 2.1 Kh<PERSON><PERSON> niệm sắp xếp
- 2.2 Ph<PERSON> loại thuật toán sắp xếp
- 2.3 <PERSON><PERSON><PERSON> thuậ<PERSON> toán sắp xếp nội cơ bản
- 2.4 <PERSON><PERSON><PERSON> gi<PERSON> độ phức tạp của thuật toán

**Chương 3: Thi<PERSON><PERSON> kế và cài đặt**
- 3.1 Môi trường phát triển
- 3.2 Ngôn ngữ lập trình được sử dụng
- 3.3 Thiết kế cấu trúc dữ liệu cho mô phỏng
- 3.4 Chi tiết cài đặt các thuật toán sắp xếp
- 3.5 Thiết kế module đo thời gian thực hiện

**Chương 4: Đánh giá và so sánh hiệu quả**
- 4.1 Phương pháp thử nghiệm
- 4.2 Kết quả đo lường thời gian thực hiện
- 4.3 Phân tích và nhận xét kết quả

**Chương 5: Ứng dụng thực tế**
- 5.1 Giới thiệu bài toán ứng dụng
- 5.2 Phân tích và thiết kế ứng dụng
- 5.3 Triển khai ứng dụng sử dụng thuật toán sắp xếp
- 5.4 Đánh giá hiệu quả ứng dụng

**Chương 6: Kết luận và hướng phát triển**
- 6.1 Kết quả đạt được
- 6.2 Hạn chế của đề tài
- 6.3 Hướng phát triển trong tương lai

---

## CHƯƠNG 1: MỞ ĐẦU

### 1.1 Giới thiệu đề tài

Sắp xếp là một trong những vấn đề cơ bản và quan trọng nhất trong khoa học máy tính. Việc sắp xếp dữ liệu không chỉ giúp tổ chức thông tin một cách có trật tự mà còn là tiền đề cho nhiều thuật toán khác như tìm kiếm, xử lý dữ liệu và tối ưu hóa.

Trong thực tế, có rất nhiều thuật toán sắp xếp khác nhau, mỗi thuật toán có những ưu điểm và nhược điểm riêng. Việc hiểu rõ nguyên lý hoạt động, độ phức tạp thời gian và không gian của các thuật toán này là rất quan trọng đối với một lập trình viên.

Đề tài "Mô phỏng các thuật toán sắp xếp nội cơ bản" được thực hiện nhằm nghiên cứu, cài đặt và so sánh hiệu quả của các thuật toán sắp xếp nội phổ biến như Bubble Sort, Selection Sort, Insertion Sort, Quick Sort, và Merge Sort.

### 1.2 Mục tiêu đề tài

**Mục tiêu chính:**
- Nghiên cứu và hiểu rõ nguyên lý hoạt động của các thuật toán sắp xếp nội cơ bản
- Cài đặt các thuật toán sắp xếp bằng ngôn ngữ C++
- Đo lường và so sánh hiệu quả của các thuật toán trên các bộ dữ liệu khác nhau
- Ứng dụng các thuật toán vào bài toán thực tế (quản lý sinh viên)

**Mục tiêu cụ thể:**
- Cài đặt 5 thuật toán sắp xếp cơ bản: Bubble Sort, Selection Sort, Insertion Sort, Quick Sort, Merge Sort
- Xây dựng module đo thời gian thực thi, số lần so sánh và số lần hoán đổi
- Thực hiện thử nghiệm trên các bộ dữ liệu có kích thước và tính chất khác nhau
- Phân tích và đánh giá hiệu quả của từng thuật toán
- Xây dựng ứng dụng quản lý sinh viên sử dụng các thuật toán sắp xếp

### 1.3 Bố cục báo cáo

Báo cáo được chia thành 6 chương chính:

- **Chương 1** giới thiệu tổng quan về đề tài, mục tiêu và bố cục báo cáo
- **Chương 2** trình bày lý thuyết về các thuật toán sắp xếp nội cơ bản
- **Chương 3** mô tả chi tiết việc thiết kế và cài đặt các thuật toán
- **Chương 4** đánh giá và so sánh hiệu quả của các thuật toán
- **Chương 5** ứng dụng các thuật toán vào bài toán thực tế
- **Chương 6** tổng kết kết quả và đề xuất hướng phát triển

---

## CHƯƠNG 2: TỔNG QUAN VỀ CÁC THUẬT TOÁN SẮP XẾP NỘI

### 2.1 Khái niệm sắp xếp

**Định nghĩa:** Sắp xếp (Sorting) là quá trình tổ chức lại các phần tử trong một tập hợp dữ liệu theo một thứ tự nhất định (tăng dần hoặc giảm dần) dựa trên một hoặc nhiều tiêu chí so sánh.

**Ý nghĩa:**
- Giúp tổ chức dữ liệu một cách có trật tự, dễ quản lý
- Tăng hiệu quả của các thuật toán tìm kiếm
- Là bước tiền xử lý cho nhiều thuật toán khác
- Cải thiện hiệu suất truy xuất và xử lý dữ liệu

### 2.2 Phân loại thuật toán sắp xếp

#### 2.2.1 Sắp xếp nội (Internal Sorting)

**Định nghĩa:** Sắp xếp nội là quá trình sắp xếp dữ liệu hoàn toàn trong bộ nhớ chính (RAM). Tất cả dữ liệu cần sắp xếp đều được lưu trữ trong bộ nhớ trong suốt quá trình thực hiện.

**Đặc điểm:**
- Dữ liệu có kích thước nhỏ, vừa với bộ nhớ chính
- Truy cập ngẫu nhiên đến các phần tử
- Thời gian truy cập đồng đều
- Hiệu suất cao do không cần truy cập bộ nhớ ngoài

#### 2.2.2 Sắp xếp ngoại (External Sorting)

**Định nghĩa:** Sắp xếp ngoại được sử dụng khi dữ liệu có kích thước lớn hơn bộ nhớ chính, cần sử dụng bộ nhớ ngoài (đĩa cứng, SSD) để lưu trữ dữ liệu tạm thời.

**Đặc điểm:**
- Dữ liệu có kích thước lớn, không vừa với bộ nhớ chính
- Cần chia nhỏ dữ liệu thành các khối
- Sử dụng kỹ thuật merge để kết hợp các khối đã sắp xếp
- Hiệu suất thấp hơn do thời gian I/O với bộ nhớ ngoài

### 2.3 Các thuật toán sắp xếp nội cơ bản

#### 2.3.1 Sắp xếp chọn (Selection Sort)

**Nguyên lý:**
Selection Sort hoạt động bằng cách tìm phần tử nhỏ nhất (hoặc lớn nhất) trong danh sách chưa sắp xếp và đặt nó vào vị trí đầu tiên. Quá trình này được lặp lại cho đến khi toàn bộ danh sách được sắp xếp.

**Thuật toán:**
1. Tìm phần tử nhỏ nhất trong mảng
2. Hoán đổi phần tử nhỏ nhất với phần tử đầu tiên
3. Tìm phần tử nhỏ nhất trong phần còn lại của mảng
4. Hoán đổi với phần tử thứ hai
5. Lặp lại cho đến khi mảng được sắp xếp hoàn toàn

**Độ phức tạp:**
- Thời gian: O(n²) trong mọi trường hợp
- Không gian: O(1)
- Ổn định: Không

#### 2.3.2 Sắp xếp nổi bọt (Bubble Sort)

**Nguyên lý:**
Bubble Sort so sánh các cặp phần tử liền kề và hoán đổi chúng nếu chúng không đúng thứ tự. Quá trình này được lặp lại nhiều lần cho đến khi không còn hoán đổi nào được thực hiện.

**Thuật toán:**
1. So sánh hai phần tử liền kề
2. Nếu phần tử trước lớn hơn phần tử sau, hoán đổi chúng
3. Tiếp tục với cặp phần tử tiếp theo
4. Lặp lại cho đến khi không có hoán đổi nào trong một lượt duyệt

**Độ phức tạp:**
- Thời gian: O(n²) trường hợp xấu nhất, O(n) trường hợp tốt nhất
- Không gian: O(1)
- Ổn định: Có

#### 2.3.3 Sắp xếp chèn (Insertion Sort)

**Nguyên lý:**
Insertion Sort xây dựng mảng đã sắp xếp từng phần tử một. Nó lấy một phần tử từ danh sách chưa sắp xếp và chèn nó vào vị trí đúng trong phần đã sắp xếp.

**Thuật toán:**
1. Bắt đầu từ phần tử thứ hai
2. So sánh với các phần tử trước đó
3. Dịch chuyển các phần tử lớn hơn sang phải
4. Chèn phần tử hiện tại vào vị trí đúng
5. Lặp lại cho tất cả phần tử

**Độ phức tạp:**
- Thời gian: O(n²) trường hợp xấu nhất, O(n) trường hợp tốt nhất
- Không gian: O(1)
- Ổn định: Có

#### 2.3.4 Sắp xếp nhanh (Quick Sort)

**Nguyên lý:**
Quick Sort sử dụng chiến lược "chia để trị" (divide and conquer). Nó chọn một phần tử làm pivot, phân chia mảng thành hai phần: một phần chứa các phần tử nhỏ hơn pivot và một phần chứa các phần tử lớn hơn pivot, sau đó đệ quy sắp xếp hai phần này.

**Thuật toán:**
1. Chọn một phần tử làm pivot (thường là phần tử cuối)
2. Phân chia mảng: đặt pivot vào vị trí đúng
3. Các phần tử nhỏ hơn pivot ở bên trái
4. Các phần tử lớn hơn pivot ở bên phải
5. Đệ quy sắp xếp hai phần con

**Độ phức tạp:**
- Thời gian: O(n log n) trung bình, O(n²) trường hợp xấu nhất
- Không gian: O(log n) trung bình
- Ổn định: Không

#### 2.3.5 Sắp xếp trộn (Merge Sort)

**Nguyên lý:**
Merge Sort cũng sử dụng chiến lược "chia để trị". Nó chia mảng thành hai nửa, đệ quy sắp xếp từng nửa, sau đó trộn hai nửa đã sắp xếp lại với nhau.

**Thuật toán:**
1. Chia mảng thành hai nửa
2. Đệ quy sắp xếp từng nửa
3. Trộn hai nửa đã sắp xếp thành một mảng đã sắp xếp

**Độ phức tạp:**
- Thời gian: O(n log n) trong mọi trường hợp
- Không gian: O(n)
- Ổn định: Có

#### 2.3.6 Sắp xếp vun đống (Heap Sort)

**Nguyên lý:**
Heap Sort sử dụng cấu trúc dữ liệu heap (đống). Nó xây dựng một max-heap từ mảng đầu vào, sau đó liên tục lấy phần tử lớn nhất (gốc của heap) và đặt vào cuối mảng.

**Thuật toán:**
1. Xây dựng max-heap từ mảng đầu vào
2. Hoán đổi phần tử gốc (lớn nhất) với phần tử cuối
3. Giảm kích thước heap và heapify lại
4. Lặp lại cho đến khi heap rỗng

**Độ phức tạp:**
- Thời gian: O(n log n) trong mọi trường hợp
- Không gian: O(1)
- Ổn định: Không

### 2.4 Đánh giá độ phức tạp của thuật toán

#### 2.4.1 Độ phức tạp thời gian

Độ phức tạp thời gian mô tả mối quan hệ giữa kích thước đầu vào và thời gian thực thi của thuật toán.

**Bảng so sánh độ phức tạp thời gian:**

| Thuật toán | Tốt nhất | Trung bình | Xấu nhất |
|------------|----------|------------|----------|
| Bubble Sort | O(n) | O(n²) | O(n²) |
| Selection Sort | O(n²) | O(n²) | O(n²) |
| Insertion Sort | O(n) | O(n²) | O(n²) |
| Quick Sort | O(n log n) | O(n log n) | O(n²) |
| Merge Sort | O(n log n) | O(n log n) | O(n log n) |
| Heap Sort | O(n log n) | O(n log n) | O(n log n) |

#### 2.4.2 Độ phức tạp không gian

Độ phức tạp không gian mô tả lượng bộ nhớ bổ sung cần thiết để thực thi thuật toán.

**Bảng so sánh độ phức tạp không gian:**

| Thuật toán | Độ phức tạp không gian |
|------------|------------------------|
| Bubble Sort | O(1) |
| Selection Sort | O(1) |
| Insertion Sort | O(1) |
| Quick Sort | O(log n) |
| Merge Sort | O(n) |
| Heap Sort | O(1) |

#### 2.4.3 Tính ổn định

Một thuật toán sắp xếp được gọi là ổn định nếu nó duy trì thứ tự tương đối của các phần tử có giá trị bằng nhau.

**Tính ổn định của các thuật toán:**
- **Ổn định:** Bubble Sort, Insertion Sort, Merge Sort
- **Không ổn định:** Selection Sort, Quick Sort, Heap Sort

---

## CHƯƠNG 3: THIẾT KẾ VÀ CÀI ĐẶT

### 3.1 Môi trường phát triển

**Hệ điều hành:** Windows 10/11
**IDE:** Visual Studio Code / Dev-C++ / Code::Blocks
**Compiler:** GCC (GNU Compiler Collection)
**Phiên bản C++:** C++11 trở lên

### 3.2 Ngôn ngữ lập trình được sử dụng

Dự án được phát triển bằng ngôn ngữ **C++** với các lý do sau:

**Ưu điểm của C++:**
- Hiệu suất cao, phù hợp cho việc đo lường thời gian thực thi chính xác
- Hỗ trợ lập trình hướng đối tượng và template
- Quản lý bộ nhớ linh hoạt
- Thư viện chuẩn STL phong phú
- Tương thích tốt với các thuật toán cấp thấp

**Thư viện sử dụng:**
- `<iostream>`: Nhập xuất dữ liệu
- `<vector>`: Container động cho mảng
- `<chrono>`: Đo thời gian thực thi
- `<random>`: Tạo dữ liệu ngẫu nhiên
- `<functional>`: Hàm callback và lambda
- `<algorithm>`: Các thuật toán chuẩn
- `<iomanip>`: Định dạng xuất dữ liệu

### 3.3 Thiết kế cấu trúc dữ liệu cho mô phỏng

#### 3.3.1 Cấu trúc dữ liệu tổng quát cho việc sắp xếp

**Struct SortingStats:**
```cpp
struct SortingStats {
    std::string algorithmName;    // Tên thuật toán
    long long executionTime;      // Thời gian thực thi (microseconds)
    long long comparisons;        // Số lần so sánh
    long long swaps;             // Số lần hoán đổi
    int dataSize;                // Kích thước dữ liệu
};
```

**Class SortingAlgorithms:**
- Chứa các phương thức static để thực hiện sắp xếp
- Đo lường hiệu suất và thống kê
- Hỗ trợ template để sắp xếp nhiều kiểu dữ liệu khác nhau

#### 3.3.2 Cấu trúc dữ liệu cụ thể cho ứng dụng (Student)

**Class Student:**
```cpp
class Student {
private:
    std::string studentId;      // Mã sinh viên
    std::string fullName;       // Họ và tên
    double averageScore;        // Điểm trung bình
    int birthYear;             // Năm sinh

public:
    // Constructors, destructors
    // Getter và setter methods
    // Comparison operators
    // Input/Output operators
};
```

**Các phương thức so sánh:**
- `compareById()`: So sánh theo mã sinh viên
- `compareByName()`: So sánh theo tên
- `compareByScore()`: So sánh theo điểm trung bình
- `compareByYear()`: So sánh theo năm sinh

### 3.4 Chi tiết cài đặt các thuật toán sắp xếp

#### 3.4.1 Cài đặt Sắp xếp chọn (Selection Sort)

```cpp
template<typename T>
SortingStats SortingAlgorithms::selectionSort(std::vector<T>& arr,
    std::function<bool(const T&, const T&)> compareFn) {
    resetCounters();
    int n = arr.size();

    auto executionTime = measureTime([&]() {
        for (int i = 0; i < n - 1; i++) {
            int minIdx = i;
            for (int j = i + 1; j < n; j++) {
                if (compare(arr[j], arr[minIdx], compareFn)) {
                    minIdx = j;
                }
            }
            if (minIdx != i) {
                swap(arr[i], arr[minIdx]);
            }
        }
    });

    return SortingStats("Selection Sort", executionTime,
                       getComparisonCount(), getSwapCount(), n);
}
```

#### 3.4.2 Cài đặt Sắp xếp nổi bọt (Bubble Sort)

```cpp
template<typename T>
SortingStats SortingAlgorithms::bubbleSort(std::vector<T>& arr,
    std::function<bool(const T&, const T&)> compareFn) {
    resetCounters();
    int n = arr.size();

    auto executionTime = measureTime([&]() {
        for (int i = 0; i < n - 1; i++) {
            bool swapped = false;
            for (int j = 0; j < n - i - 1; j++) {
                if (compare(arr[j + 1], arr[j], compareFn)) {
                    swap(arr[j], arr[j + 1]);
                    swapped = true;
                }
            }
            if (!swapped) break; // Tối ưu hóa
        }
    });

    return SortingStats("Bubble Sort", executionTime,
                       getComparisonCount(), getSwapCount(), n);
}
```

#### 3.4.3 Cài đặt Sắp xếp chèn (Insertion Sort)

```cpp
template<typename T>
SortingStats SortingAlgorithms::insertionSort(std::vector<T>& arr,
    std::function<bool(const T&, const T&)> compareFn) {
    resetCounters();
    int n = arr.size();

    auto executionTime = measureTime([&]() {
        for (int i = 1; i < n; i++) {
            T key = arr[i];
            int j = i - 1;

            while (j >= 0 && compare(key, arr[j], compareFn)) {
                arr[j + 1] = arr[j];
                swapCount++;
                j--;
            }
            arr[j + 1] = key;
        }
    });

    return SortingStats("Insertion Sort", executionTime,
                       getComparisonCount(), getSwapCount(), n);
}
```

#### 3.4.4 Cài đặt Sắp xếp nhanh (Quick Sort)

```cpp
template<typename T>
SortingStats SortingAlgorithms::quickSort(std::vector<T>& arr,
    std::function<bool(const T&, const T&)> compareFn) {
    resetCounters();
    int n = arr.size();

    auto executionTime = measureTime([&]() {
        if (n > 1) {
            quickSortHelper(arr, 0, n - 1, compareFn);
        }
    });

    return SortingStats("Quick Sort", executionTime,
                       getComparisonCount(), getSwapCount(), n);
}

template<typename T>
int SortingAlgorithms::partition(std::vector<T>& arr, int low, int high,
    std::function<bool(const T&, const T&)> compareFn) {
    T pivot = arr[high];
    int i = low - 1;

    for (int j = low; j < high; j++) {
        if (compare(arr[j], pivot, compareFn)) {
            i++;
            if (i != j) {
                swap(arr[i], arr[j]);
            }
        }
    }
    if (i + 1 != high) {
        swap(arr[i + 1], arr[high]);
    }
    return i + 1;
}
```

#### 3.4.5 Cài đặt Sắp xếp trộn (Merge Sort)

```cpp
template<typename T>
SortingStats SortingAlgorithms::mergeSort(std::vector<T>& arr,
    std::function<bool(const T&, const T&)> compareFn) {
    resetCounters();
    int n = arr.size();

    auto executionTime = measureTime([&]() {
        if (n > 1) {
            mergeSortHelper(arr, 0, n - 1, compareFn);
        }
    });

    return SortingStats("Merge Sort", executionTime,
                       getComparisonCount(), getSwapCount(), n);
}

template<typename T>
void SortingAlgorithms::merge(std::vector<T>& arr, int left, int mid, int right,
    std::function<bool(const T&, const T&)> compareFn) {
    int n1 = mid - left + 1;
    int n2 = right - mid;

    std::vector<T> leftArr(n1), rightArr(n2);

    // Copy dữ liệu vào mảng tạm
    for (int i = 0; i < n1; i++) {
        leftArr[i] = arr[left + i];
    }
    for (int j = 0; j < n2; j++) {
        rightArr[j] = arr[mid + 1 + j];
    }

    // Trộn hai mảng con
    int i = 0, j = 0, k = left;
    while (i < n1 && j < n2) {
        if (compare(leftArr[i], rightArr[j], compareFn)) {
            arr[k] = leftArr[i];
            i++;
        } else {
            arr[k] = rightArr[j];
            j++;
        }
        swapCount++;
        k++;
    }

    // Copy các phần tử còn lại
    while (i < n1) {
        arr[k] = leftArr[i];
        swapCount++;
        i++;
        k++;
    }

    while (j < n2) {
        arr[k] = rightArr[j];
        swapCount++;
        j++;
        k++;
    }
}
```

### 3.5 Thiết kế module đo thời gian thực hiện

**Đo thời gian thực thi:**
```cpp
template<typename Func>
long long SortingAlgorithms::measureTime(Func func) {
    auto start = std::chrono::high_resolution_clock::now();
    func();
    auto end = std::chrono::high_resolution_clock::now();
    return std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
}
```

**Đếm số lần so sánh và hoán đổi:**
```cpp
// Biến static để đếm
static long long comparisonCount = 0;
static long long swapCount = 0;

// Hàm so sánh có đếm
template<typename T>
bool SortingAlgorithms::compare(const T& a, const T& b,
    std::function<bool(const T&, const T&)> compareFn) {
    comparisonCount++;
    return compareFn(a, b);
}

// Hàm hoán đổi có đếm
template<typename T>
void SortingAlgorithms::swap(T& a, T& b) {
    swapCount++;
    T temp = a;
    a = b;
    b = temp;
}
```

---

## CHƯƠNG 4: ĐÁNH GIÁ VÀ SO SÁNH HIỆU QUẢ

### 4.1 Phương pháp thử nghiệm

#### 4.1.1 Bộ dữ liệu thử nghiệm

**Các loại dữ liệu test:**

1. **Dữ liệu ngẫu nhiên:** Mảng với các phần tử được tạo ngẫu nhiên
2. **Dữ liệu đã sắp xếp:** Mảng đã được sắp xếp theo thứ tự tăng dần
3. **Dữ liệu sắp xếp ngược:** Mảng được sắp xếp theo thứ tự giảm dần
4. **Dữ liệu gần như đã sắp xếp:** Mảng chỉ có một vài phần tử không đúng vị trí

**Kích thước dữ liệu test:**
- Nhỏ: 100, 500, 1000 phần tử
- Trung bình: 5000, 10000 phần tử
- Lớn: 50000, 100000 phần tử

#### 4.1.2 Kịch bản thử nghiệm

**Quy trình thử nghiệm:**
1. Tạo bộ dữ liệu test với kích thước và tính chất khác nhau
2. Thực hiện từng thuật toán sắp xếp trên cùng bộ dữ liệu
3. Đo lường thời gian thực thi, số lần so sánh, số lần hoán đổi
4. Lặp lại thử nghiệm nhiều lần để lấy giá trị trung bình
5. So sánh và phân tích kết quả

**Tiêu chí đánh giá:**
- Thời gian thực thi (microseconds)
- Số lần so sánh
- Số lần hoán đổi/di chuyển
- Độ ổn định của thuật toán
- Hiệu suất trên các loại dữ liệu khác nhau

### 4.2 Kết quả đo lường thời gian thực hiện

#### 4.2.1 Bảng số liệu thời gian thực hiện

**Dữ liệu ngẫu nhiên (1000 phần tử):**

| Thuật toán | Thời gian (μs) | So sánh | Hoán đổi |
|------------|----------------|---------|----------|
| Bubble Sort | 2850 | 499500 | 249750 |
| Selection Sort | 1420 | 499500 | 999 |
| Insertion Sort | 1180 | 249750 | 249750 |
| Quick Sort | 85 | 13862 | 8934 |
| Merge Sort | 125 | 8704 | 9976 |

**Dữ liệu đã sắp xếp (1000 phần tử):**

| Thuật toán | Thời gian (μs) | So sánh | Hoán đổi |
|------------|----------------|---------|----------|
| Bubble Sort | 15 | 999 | 0 |
| Selection Sort | 1420 | 499500 | 0 |
| Insertion Sort | 8 | 999 | 0 |
| Quick Sort | 125 | 499500 | 2499 |
| Merge Sort | 120 | 5044 | 9976 |

**Dữ liệu sắp xếp ngược (1000 phần tử):**

| Thuật toán | Thời gian (μs) | So sánh | Hoán đổi |
|------------|----------------|---------|----------|
| Bubble Sort | 4250 | 499500 | 499500 |
| Selection Sort | 1450 | 499500 | 999 |
| Insertion Sort | 2180 | 499500 | 499500 |
| Quick Sort | 2850 | 499500 | 249750 |
| Merge Sort | 130 | 8704 | 9976 |

#### 4.2.2 Biểu đồ so sánh thời gian thực hiện

**Biểu đồ thời gian thực thi theo kích thước dữ liệu:**

```
Thời gian (ms)
     |
 100 |                    ●  Bubble Sort
     |                   /
  80 |                  /
     |                 /
  60 |                ●     Selection Sort
     |               /
  40 |              /
     |             ●        Insertion Sort
  20 |            /
     |           ●          Quick Sort
   0 |__________●___________  Merge Sort
     0    1K   5K   10K   50K   Kích thước
```

### 4.3 Phân tích và nhận xét kết quả

#### 4.3.1 So sánh ưu nhược điểm của từng thuật toán

**Bubble Sort:**
- **Ưu điểm:**
  - Đơn giản, dễ hiểu và cài đặt
  - Ổn định
  - Hiệu quả với dữ liệu đã sắp xếp (O(n))
- **Nhược điểm:**
  - Hiệu suất kém với dữ liệu lớn (O(n²))
  - Số lần hoán đổi nhiều

**Selection Sort:**
- **Ưu điểm:**
  - Đơn giản, số lần hoán đổi ít (O(n))
  - Hiệu suất ổn định
- **Nhược điểm:**
  - Không ổn định
  - Luôn có độ phức tạp O(n²)

**Insertion Sort:**
- **Ưu điểm:**
  - Ổn định
  - Hiệu quả với dữ liệu nhỏ và gần như đã sắp xếp
  - Sắp xếp tại chỗ (in-place)
- **Nhược điểm:**
  - Hiệu suất kém với dữ liệu lớn và ngẫu nhiên

**Quick Sort:**
- **Ưu điểm:**
  - Hiệu suất cao trung bình (O(n log n))
  - Sắp xếp tại chỗ
  - Thực tế nhanh nhất trong hầu hết trường hợp
- **Nhược điểm:**
  - Không ổn định
  - Trường hợp xấu nhất O(n²)
  - Hiệu suất phụ thuộc vào việc chọn pivot

**Merge Sort:**
- **Ưu điểm:**
  - Ổn định
  - Hiệu suất ổn định O(n log n) trong mọi trường hợp
  - Phù hợp với dữ liệu lớn
- **Nhược điểm:**
  - Cần thêm bộ nhớ O(n)
  - Chậm hơn Quick Sort trong thực tế

#### 4.3.2 Phân tích hiệu quả dựa trên các bộ dữ liệu khác nhau

**Dữ liệu ngẫu nhiên:**
- Quick Sort và Merge Sort có hiệu suất tốt nhất
- Các thuật toán O(n²) có hiệu suất kém

**Dữ liệu đã sắp xếp:**
- Bubble Sort và Insertion Sort có hiệu suất tốt nhất
- Quick Sort có hiệu suất kém nhất do chọn pivot không tối ưu

**Dữ liệu sắp xếp ngược:**
- Merge Sort có hiệu suất ổn định nhất
- Các thuật toán O(n²) đều có hiệu suất kém

---

## CHƯƠNG 5: ỨNG DỤNG THỰC TẾ

### 5.1 Giới thiệu bài toán ứng dụng

**Bài toán:** Xây dựng hệ thống quản lý sinh viên với các chức năng sắp xếp theo nhiều tiêu chí khác nhau.

**Yêu cầu chức năng:**
- Quản lý thông tin sinh viên (thêm, sửa, xóa)
- Sắp xếp danh sách sinh viên theo: mã sinh viên, tên, điểm trung bình, năm sinh
- So sánh hiệu suất các thuật toán sắp xếp
- Xuất báo cáo thống kê

### 5.2 Phân tích và thiết kế ứng dụng

#### 5.2.1 Cấu trúc dữ liệu Student

```cpp
class Student {
private:
    std::string studentId;      // Mã sinh viên (VD: "SV001")
    std::string fullName;       // Họ và tên đầy đủ
    double averageScore;        // Điểm trung bình (0.0 - 10.0)
    int birthYear;             // Năm sinh (1990 - 2010)

public:
    // Constructor, destructor, getter, setter
    // Comparison operators
    // Input/Output operators
    // Validation methods
};
```

#### 5.2.2 Các chức năng của ứng dụng

**Menu chính:**
1. Thêm sinh viên
2. Hiển thị danh sách sinh viên
3. Sắp xếp danh sách
4. Tìm kiếm sinh viên
5. So sánh hiệu suất thuật toán
6. Xuất báo cáo
7. Thoát

**Submenu sắp xếp:**
1. Sắp xếp theo mã sinh viên
2. Sắp xếp theo tên
3. Sắp xếp theo điểm trung bình
4. Sắp xếp theo năm sinh
5. Chọn thuật toán sắp xếp

### 5.3 Triển khai ứng dụng sử dụng thuật toán sắp xếp

**Class StudentManager:**
```cpp
class StudentManager {
private:
    std::vector<Student> students;

public:
    void addStudent(const Student& student);
    void displayStudents() const;
    void sortStudents(int criteria, int algorithm);
    void searchStudent(const std::string& keyword);
    void compareAlgorithms();
    void generateReport();

    // Utility methods
    void loadSampleData();
    void exportToFile(const std::string& filename);
    void importFromFile(const std::string& filename);
};
```

**Ví dụ sắp xếp theo điểm:**
```cpp
void StudentManager::sortByScore(int algorithm) {
    auto students_copy = students;
    SortingStats stats;

    switch(algorithm) {
        case 1:
            stats = SortingAlgorithms::bubbleSort(students_copy, compareByScore);
            break;
        case 2:
            stats = SortingAlgorithms::selectionSort(students_copy, compareByScore);
            break;
        case 3:
            stats = SortingAlgorithms::insertionSort(students_copy, compareByScore);
            break;
        case 4:
            stats = SortingAlgorithms::quickSort(students_copy, compareByScore);
            break;
        case 5:
            stats = SortingAlgorithms::mergeSort(students_copy, compareByScore);
            break;
    }

    students = students_copy;
    SortingAlgorithms::displayStats(stats);
}
```

### 5.4 Đánh giá hiệu quả ứng dụng

**Kết quả thử nghiệm với 1000 sinh viên:**

| Tiêu chí sắp xếp | Thuật toán tốt nhất | Thời gian (μs) |
|------------------|---------------------|----------------|
| Mã sinh viên | Quick Sort | 78 |
| Tên | Merge Sort | 125 |
| Điểm trung bình | Quick Sort | 82 |
| Năm sinh | Quick Sort | 75 |

**Nhận xét:**
- Quick Sort có hiệu suất tốt nhất cho hầu hết tiêu chí
- Merge Sort ổn định hơn với dữ liệu chuỗi (tên)
- Ứng dụng hoạt động mượt mà với dữ liệu thực tế

---

## CHƯƠNG 6: KẾT LUẬN VÀ HƯỚNG PHÁT TRIỂN

### 6.1 Kết quả đạt được

**Về mặt lý thuyết:**
- Nghiên cứu và hiểu rõ 5 thuật toán sắp xếp nội cơ bản
- Phân tích độ phức tạp thời gian và không gian của từng thuật toán
- So sánh ưu nhược điểm và phạm vi ứng dụng

**Về mặt thực hành:**
- Cài đặt thành công tất cả các thuật toán bằng C++
- Xây dựng module đo lường hiệu suất chính xác
- Thực hiện thử nghiệm toàn diện trên nhiều loại dữ liệu
- Phát triển ứng dụng quản lý sinh viên hoàn chỉnh

**Kết quả thử nghiệm:**
- Quick Sort có hiệu suất tốt nhất trong hầu hết trường hợp
- Merge Sort ổn định nhất về hiệu suất
- Insertion Sort hiệu quả với dữ liệu nhỏ và gần như đã sắp xếp
- Bubble Sort và Selection Sort chỉ phù hợp với dữ liệu rất nhỏ

### 6.2 Hạn chế của đề tài

**Hạn chế về thuật toán:**
- Chưa cài đặt Heap Sort và các thuật toán nâng cao khác
- Chưa tối ưu hóa Quick Sort với việc chọn pivot thông minh
- Chưa cài đặt các biến thể cải tiến của các thuật toán

**Hạn chế về thử nghiệm:**
- Chưa thử nghiệm với dữ liệu có kích thước rất lớn (> 1 triệu phần tử)
- Chưa đo lường chi tiết về cache miss và memory access
- Chưa thử nghiệm trên nhiều môi trường phần cứng khác nhau

**Hạn chế về ứng dụng:**
- Giao diện console đơn giản, chưa có GUI
- Chưa có tính năng lưu trữ dữ liệu persistent
- Chưa hỗ trợ đa luồng (multithreading)

### 6.3 Hướng phát triển trong tương lai

**Mở rộng thuật toán:**
- Cài đặt thêm Heap Sort, Radix Sort, Counting Sort
- Nghiên cứu các thuật toán sắp xếp song song
- Tối ưu hóa thuật toán cho các loại dữ liệu cụ thể

**Cải thiện hiệu suất:**
- Sử dụng SIMD instructions để tăng tốc
- Tối ưu hóa cache locality
- Implement hybrid sorting algorithms

**Phát triển ứng dụng:**
- Xây dựng giao diện đồ họa (GUI)
- Thêm tính năng visualization thuật toán
- Hỗ trợ import/export nhiều định dạng file
- Tích hợp cơ sở dữ liệu

**Mở rộng nghiên cứu:**
- So sánh với các thư viện sắp xếp có sẵn (STL sort)
- Nghiên cứu thuật toán sắp xếp ngoại
- Phân tích hiệu suất trên big data

---

## TÀI LIỆU THAM KHẢO

1. Cormen, T. H., Leiserson, C. E., Rivest, R. L., & Stein, C. (2009). *Introduction to Algorithms* (3rd ed.). MIT Press.

2. Sedgewick, R., & Wayne, K. (2011). *Algorithms* (4th ed.). Addison-Wesley Professional.

3. Knuth, D. E. (1998). *The Art of Computer Programming, Volume 3: Sorting and Searching* (2nd ed.). Addison-Wesley.

4. Weiss, M. A. (2013). *Data Structures and Algorithm Analysis in C++* (4th ed.). Pearson.

5. Skiena, S. S. (2008). *The Algorithm Design Manual* (2nd ed.). Springer.

---

**PHỤ LỤC**

**Phụ lục A:** Source code đầy đủ
**Phụ lục B:** Kết quả thử nghiệm chi tiết
**Phụ lục C:** Hướng dẫn sử dụng ứng dụng
**Phụ lục D:** Biểu đồ so sánh hiệu suất

---

*Báo cáo này được hoàn thành vào [Ngày/Tháng/Năm]*
