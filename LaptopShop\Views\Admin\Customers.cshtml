@model List<LaptopShop.Models.ApplicationUser>
@{
    ViewData["Title"] = "Quản Lý Khách Hàng";
}

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 admin-sidebar">
            <div class="d-flex flex-column p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-laptop me-2"></i>LaptopShop Admin
                </h5>
                
                <ul class="nav nav-pills flex-column">
                    <li class="nav-item">
                        <a class="nav-link" asp-action="Index">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-action="Laptops">
                            <i class="fas fa-laptop me-2"></i>Quản lý Laptop
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-action="Orders">
                            <i class="fas fa-shopping-cart me-2"></i>Quản lý Đơn hàng
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" asp-action="Customers">
                            <i class="fas fa-users me-2"></i>Quản lý Khách hàng
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-action="Categories">
                            <i class="fas fa-tags me-2"></i>Danh mục
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-action="Brands">
                            <i class="fas fa-trademark me-2"></i>Thương hiệu
                        </a>
                    </li>
                </ul>

                <hr class="text-white">
                
                <div class="mt-auto">
                    <a class="nav-link text-light" asp-controller="Home" asp-action="Index" target="_blank">
                        <i class="fas fa-external-link-alt me-2"></i>Xem Website
                    </a>
                    <a class="nav-link text-light" asp-action="Logout">
                        <i class="fas fa-sign-out-alt me-2"></i>Đăng xuất
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <div class="p-4">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="gradient-text">Quản Lý Khách Hàng</h2>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary">
                            <i class="fas fa-download me-2"></i>Xuất Excel
                        </button>
                        <button class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Thêm Khách Hàng
                        </button>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-users fa-2x text-primary mb-2"></i>
                                <h4 class="text-primary">@(Model?.Count ?? 0)</h4>
                                <p class="text-muted mb-0">Tổng khách hàng</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-user-check fa-2x text-success mb-2"></i>
                                <h4 class="text-success">@(Model?.Count(u => u.IsActive) ?? 0)</h4>
                                <p class="text-muted mb-0">Đang hoạt động</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-user-plus fa-2x text-info mb-2"></i>
                                <h4 class="text-info">@(Model?.Count(u => u.CreatedAt.Date == DateTime.Today) ?? 0)</h4>
                                <p class="text-muted mb-0">Mới hôm nay</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-user-times fa-2x text-warning mb-2"></i>
                                <h4 class="text-warning">@(Model?.Count(u => !u.IsActive) ?? 0)</h4>
                                <p class="text-muted mb-0">Bị khóa</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Search & Filter -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="Tìm kiếm khách hàng...">
                            <button class="btn btn-outline-secondary" type="button">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select">
                            <option value="">Tất cả trạng thái</option>
                            <option value="active">Đang hoạt động</option>
                            <option value="inactive">Bị khóa</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <input type="date" class="form-control" placeholder="Từ ngày">
                    </div>
                    <div class="col-md-2">
                        <select class="form-select">
                            <option value="">Sắp xếp</option>
                            <option value="name">Theo tên</option>
                            <option value="date">Theo ngày</option>
                            <option value="orders">Theo đơn hàng</option>
                        </select>
                    </div>
                </div>

                <!-- Customers Table -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th>Họ tên</th>
                                        <th>Email</th>
                                        <th>Số điện thoại</th>
                                        <th>Địa chỉ</th>
                                        <th>Ngày đăng ký</th>
                                        <th>Trạng thái</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (Model != null && Model.Any())
                                    {
                                        @foreach (var customer in Model)
                                        {
                                            <tr>
                                                <td>@customer.Id</td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-circle me-2">
                                                            @customer.FullName.Substring(0, 1).ToUpper()
                                                        </div>
                                                        <div>
                                                            <strong>@customer.FullName</strong>
                                                            <br>
                                                            <small class="text-muted">@customer.UserName</small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    @customer.Email
                                                    @if (customer.EmailConfirmed)
                                                    {
                                                        <i class="fas fa-check-circle text-success ms-1" title="Email đã xác thực"></i>
                                                    }
                                                    else
                                                    {
                                                        <i class="fas fa-exclamation-circle text-warning ms-1" title="Email chưa xác thực"></i>
                                                    }
                                                </td>
                                                <td>
                                                    @(customer.PhoneNumber ?? "Chưa cập nhật")
                                                    @if (customer.PhoneNumberConfirmed)
                                                    {
                                                        <i class="fas fa-check-circle text-success ms-1" title="SĐT đã xác thực"></i>
                                                    }
                                                </td>
                                                <td>
                                                    @if (!string.IsNullOrEmpty(customer.Address))
                                                    {
                                                        <span title="@customer.Address">
                                                            @(customer.Address.Length > 30 ? customer.Address.Substring(0, 30) + "..." : customer.Address)
                                                        </span>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">Chưa cập nhật</span>
                                                    }
                                                </td>
                                                <td>@customer.CreatedAt.ToString("dd/MM/yyyy")</td>
                                                <td>
                                                    @if (customer.IsActive)
                                                    {
                                                        <span class="badge bg-success">Hoạt động</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-danger">Bị khóa</span>
                                                    }
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="#" class="btn btn-sm btn-outline-primary" title="Xem chi tiết">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <button class="btn btn-sm btn-outline-warning" title="Chỉnh sửa">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        @if (customer.IsActive)
                                                        {
                                                            <button class="btn btn-sm btn-outline-danger" title="Khóa tài khoản">
                                                                <i class="fas fa-lock"></i>
                                                            </button>
                                                        }
                                                        else
                                                        {
                                                            <button class="btn btn-sm btn-outline-success" title="Mở khóa">
                                                                <i class="fas fa-unlock"></i>
                                                            </button>
                                                        }
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                    }
                                    else
                                    {
                                        <tr>
                                            <td colspan="8" class="text-center py-4">
                                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                                <p class="text-muted">Chưa có khách hàng nào trong hệ thống</p>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                @if (Model != null && Model.Any())
                {
                    <nav aria-label="Customer pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1">Trước</a>
                            </li>
                            <li class="page-item active">
                                <a class="page-link" href="#">1</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">2</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">3</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">Sau</a>
                            </li>
                        </ul>
                    </nav>
                }
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 16px;
}
</style>
