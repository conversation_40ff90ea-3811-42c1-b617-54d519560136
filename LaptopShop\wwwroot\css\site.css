html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
  text-align: start;
}

/* Custom Styles for LaptopShop */

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,255,0.1)"><polygon points="1000,100 1000,0 0,100"/></svg>');
  background-size: cover;
}

.hero-section .container {
  position: relative;
  z-index: 1;
}

/* Product Cards */
.product-card {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: none;
  border-radius: 15px;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.product-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.product-card .card-img-top {
  transition: transform 0.4s ease;
  border-radius: 15px 15px 0 0;
}

.product-card:hover .card-img-top {
  transform: scale(1.1);
}

.category-card {
  transition: all 0.3s ease;
  border: none;
  border-radius: 15px;
  background: linear-gradient(145deg, #f8f9fa, #e9ecef);
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  background: linear-gradient(145deg, #fff, #f8f9fa);
}

.brand-card {
  transition: all 0.3s ease;
  border: none;
  border-radius: 10px;
  background: linear-gradient(145deg, #ffffff, #f1f3f4);
  box-shadow: 0 2px 10px rgba(0,0,0,0.08);
}

.brand-card:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 20px rgba(0,0,0,0.15);
  background: linear-gradient(145deg, #f8f9fa, #ffffff);
}

/* Price styling */
.price-section {
  font-weight: 600;
}

/* Animated price badges */
.badge {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Cart badge */
#cart-count {
  font-size: 0.7rem;
  min-width: 1.2rem;
  height: 1.2rem;
  display: none;
  animation: bounce 0.5s ease;
}

@keyframes bounce {
  0%, 20%, 60%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  80% { transform: translateY(-5px); }
}

/* Navbar */
.navbar-brand {
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(45deg, #fff, #f8f9fa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.navbar {
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px rgba(0,0,0,0.1);
}

/* Search form */
.navbar .input-group {
  width: 300px;
}

.navbar .input-group .form-control {
  border-radius: 25px 0 0 25px;
  border: 2px solid rgba(255,255,255,0.2);
  background: rgba(255,255,255,0.1);
  color: white;
}

.navbar .input-group .form-control::placeholder {
  color: rgba(255,255,255,0.7);
}

.navbar .input-group .btn {
  border-radius: 0 25px 25px 0;
  border: 2px solid rgba(255,255,255,0.2);
  border-left: none;
}

@media (max-width: 768px) {
  .navbar .input-group {
    width: 100%;
    margin-top: 1rem;
  }
}

/* Product grid */
.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

/* Filters */
.filters-sidebar {
  background-color: #f8f9fa;
  border-radius: 0.5rem;
  padding: 1.5rem;
}

/* Pagination */
.pagination .page-link {
  color: #0d6efd;
}

.pagination .page-item.active .page-link {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

/* Admin styles */
.admin-sidebar {
  background-color: #343a40;
  min-height: calc(100vh - 56px);
}

.admin-sidebar .nav-link {
  color: #adb5bd;
  padding: 0.75rem 1rem;
}

.admin-sidebar .nav-link:hover,
.admin-sidebar .nav-link.active {
  color: #fff;
  background-color: #495057;
}

/* Dashboard cards */
.dashboard-card {
  border-left: 4px solid #0d6efd;
}

.dashboard-card.success {
  border-left-color: #198754;
}

.dashboard-card.warning {
  border-left-color: #ffc107;
}

.dashboard-card.danger {
  border-left-color: #dc3545;
}

/* Tables */
.table-responsive {
  border-radius: 0.5rem;
  overflow: hidden;
}

/* Forms */
.form-control:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Buttons */
.btn {
  border-radius: 25px;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border: none;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary:hover {
  background: linear-gradient(45deg, #764ba2, #667eea);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.btn-outline-primary {
  border: 2px solid #667eea;
  color: #667eea;
  background: transparent;
}

.btn-outline-primary:hover {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

/* Alerts */
.alert {
  border: none;
  border-radius: 0.5rem;
}

/* Loading spinner */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Loading animations */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Image hover effects */
.img-hover-zoom {
  overflow: hidden;
  border-radius: 15px;
}

.img-hover-zoom img {
  transition: transform 0.5s ease;
}

.img-hover-zoom:hover img {
  transform: scale(1.1);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Floating elements */
.float-animation {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* Section dividers */
.section-divider {
  height: 2px;
  background: linear-gradient(90deg, transparent, #667eea, transparent);
  margin: 3rem 0;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #764ba2, #667eea);
}

/* Responsive utilities */
@media (max-width: 576px) {
  .hero-section h1 {
    font-size: 2rem;
  }

  .hero-section .lead {
    font-size: 1rem;
  }

  .product-card {
    margin-bottom: 1rem;
  }

  .navbar-brand {
    font-size: 1.2rem;
  }
}