@model ShoeShopAnNhien.ViewModels.AdminDashboardViewModel
@{
    ViewData["Title"] = "Dashboard - Admin Panel";
    Layout = "_AdminLayout";
}

<!-- Page Header -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="h3 mb-0">Dashboard</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item active">Dashboard</li>
                </ol>
            </nav>
        </div>
        <div>
            <span class="text-muted">Cập nhật lần cuối: @DateTime.Now.ToString("dd/MM/yyyy HH:mm")</span>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card dashboard-card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Tổng Sản Phẩm</div>
                        <div class="h5 mb-0 font-weight-bold">@Model.TotalProducts</div>
                    </div>
                    <div class="card-icon">
                        <i class="fas fa-box"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card dashboard-card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Tổng Đơn Hàng</div>
                        <div class="h5 mb-0 font-weight-bold">@Model.TotalOrders</div>
                    </div>
                    <div class="card-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card dashboard-card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Khách Hàng</div>
                        <div class="h5 mb-0 font-weight-bold">@Model.TotalCustomers</div>
                    </div>
                    <div class="card-icon">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card dashboard-card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Doanh Thu</div>
                        <div class="h5 mb-0 font-weight-bold">@Model.TotalRevenue.ToString("N0") VNĐ</div>
                    </div>
                    <div class="card-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Orders -->
    <div class="col-lg-8 mb-4">
        <div class="card admin-table">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Đơn Hàng Gần Đây</h5>
                <a href="@Url.Action("Orders", "Admin")" class="btn btn-primary btn-sm">Xem Tất Cả</a>
            </div>
            <div class="card-body p-0">
                @if (Model.RecentOrders.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Mã Đơn</th>
                                    <th>Khách Hàng</th>
                                    <th>Tổng Tiền</th>
                                    <th>Trạng Thái</th>
                                    <th>Ngày Đặt</th>
                                    <th>Thao Tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var order in Model.RecentOrders)
                                {
                                    <tr>
                                        <td class="fw-bold">#@order.OrderNumber</td>
                                        <td>@order.CustomerName</td>
                                        <td class="fw-bold text-success">@order.TotalAmount.ToString("N0") VNĐ</td>
                                        <td>
                                            @{
                                                var statusClass = order.Status switch
                                                {
                                                    OrderStatus.Pending => "bg-warning",
                                                    OrderStatus.Confirmed => "bg-info",
                                                    OrderStatus.Processing => "bg-primary",
                                                    OrderStatus.Shipping => "bg-secondary",
                                                    OrderStatus.Delivered => "bg-success",
                                                    OrderStatus.Cancelled => "bg-danger",
                                                    _ => "bg-secondary"
                                                };
                                                
                                                var statusText = order.Status switch
                                                {
                                                    OrderStatus.Pending => "Chờ xử lý",
                                                    OrderStatus.Confirmed => "Đã xác nhận",
                                                    OrderStatus.Processing => "Đang xử lý",
                                                    OrderStatus.Shipping => "Đang giao",
                                                    OrderStatus.Delivered => "Đã giao",
                                                    OrderStatus.Cancelled => "Đã hủy",
                                                    _ => "Không xác định"
                                                };
                                            }
                                            <span class="badge @statusClass">@statusText</span>
                                        </td>
                                        <td>@order.CreatedAt.ToString("dd/MM/yyyy")</td>
                                        <td>
                                            <div class="action-buttons">
                                                @if (order.Status == OrderStatus.Pending)
                                                {
                                                    <button class="btn btn-success btn-sm" onclick="updateOrderStatus(@order.Id, @((int)OrderStatus.Confirmed))">
                                                        Xác Nhận
                                                    </button>
                                                }
                                                @if (order.Status == OrderStatus.Confirmed)
                                                {
                                                    <button class="btn btn-primary btn-sm" onclick="updateOrderStatus(@order.Id, @((int)OrderStatus.Processing))">
                                                        Xử Lý
                                                    </button>
                                                }
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-4">
                        <i class="fas fa-shopping-cart text-muted fs-1 mb-3"></i>
                        <p class="text-muted">Chưa có đơn hàng nào</p>
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Thao Tác Nhanh</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="@Url.Action("Products", "Admin")" class="btn btn-outline-primary">
                        <i class="fas fa-plus me-2"></i>Thêm Sản Phẩm Mới
                    </a>
                    <a href="@Url.Action("Orders", "Admin")" class="btn btn-outline-success">
                        <i class="fas fa-list me-2"></i>Xem Đơn Hàng
                    </a>
                    <a href="@Url.Action("Customers", "Admin")" class="btn btn-outline-info">
                        <i class="fas fa-users me-2"></i>Quản Lý Khách Hàng
                    </a>
                    <a href="@Url.Action("Statistics", "Admin")" class="btn btn-outline-warning">
                        <i class="fas fa-chart-bar me-2"></i>Xem Thống Kê
                    </a>
                </div>
            </div>
        </div>

        <!-- Low Stock Alert -->
        @if (Model.LowStockProducts.Any())
        {
            <div class="card mt-4">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>Cảnh Báo Tồn Kho
                    </h6>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        @foreach (var product in Model.LowStockProducts)
                        {
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">@product.Name</h6>
                                    <small class="text-muted">@product.Brand.Name</small>
                                </div>
                                <span class="badge bg-danger rounded-pill">@product.StockQuantity</span>
                            </div>
                        }
                    </div>
                </div>
            </div>
        }
    </div>
</div>

<!-- Top Products -->
<div class="row">
    <div class="col-12">
        <div class="card admin-table">
            <div class="card-header">
                <h5 class="mb-0">Sản Phẩm Nổi Bật</h5>
            </div>
            <div class="card-body p-0">
                @if (Model.TopProducts.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Hình Ảnh</th>
                                    <th>Tên Sản Phẩm</th>
                                    <th>Thương Hiệu</th>
                                    <th>Danh Mục</th>
                                    <th>Giá</th>
                                    <th>Tồn Kho</th>
                                    <th>Trạng Thái</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var product in Model.TopProducts)
                                {
                                    <tr>
                                        <td>
                                            <img src="@(product.MainImageUrl ?? "/images/products/default.jpg")" 
                                                 alt="@product.Name" 
                                                 class="product-image">
                                        </td>
                                        <td>
                                            <div>
                                                <h6 class="mb-1">@product.Name</h6>
                                                <small class="text-muted">SKU: @product.SKU</small>
                                            </div>
                                        </td>
                                        <td>@product.Brand.Name</td>
                                        <td>@product.Category.Name</td>
                                        <td>
                                            @if (product.IsOnSale)
                                            {
                                                <span class="text-danger fw-bold">@product.SalePrice?.ToString("N0") VNĐ</span>
                                                <br><small class="text-muted text-decoration-line-through">@product.Price.ToString("N0") VNĐ</small>
                                            }
                                            else
                                            {
                                                <span class="fw-bold">@product.Price.ToString("N0") VNĐ</span>
                                            }
                                        </td>
                                        <td>
                                            <span class="badge @(product.StockQuantity <= 10 ? "bg-danger" : "bg-success")">
                                                @product.StockQuantity
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge @(product.IsActive ? "bg-success" : "bg-secondary")">
                                                @(product.IsActive ? "Hoạt động" : "Tạm dừng")
                                            </span>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-4">
                        <i class="fas fa-box text-muted fs-1 mb-3"></i>
                        <p class="text-muted">Chưa có sản phẩm nào</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>
