using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using LaptopShop.Data;
using LaptopShop.Models;

namespace LaptopShop.Controllers
{
    [Authorize(Roles = "Admin")]
    public class AdminController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;

        public AdminController(ApplicationDbContext context, UserManager<ApplicationUser> userManager, SignInManager<ApplicationUser> signInManager)
        {
            _context = context;
            _userManager = userManager;
            _signInManager = signInManager;
        }

        [AllowAnonymous]
        public IActionResult Login()
        {
            if (User.Identity?.IsAuthenticated == true && User.IsInRole("Admin"))
            {
                return RedirectToAction("Index");
            }
            
            ViewData["Title"] = "Đăng Nhập Admin";
            return View();
        }

        [HttpPost]
        [AllowAnonymous]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Login(AdminLoginViewModel model)
        {
            ViewData["Title"] = "Đăng Nhập Admin";
            
            if (ModelState.IsValid)
            {
                var result = await _signInManager.PasswordSignInAsync(
                    model.Username, model.Password, model.RememberMe, lockoutOnFailure: false);

                if (result.Succeeded)
                {
                    var user = await _userManager.FindByNameAsync(model.Username);
                    if (user != null && await _userManager.IsInRoleAsync(user, "Admin"))
                    {
                        return RedirectToAction("Index");
                    }
                    else
                    {
                        await _signInManager.SignOutAsync();
                        ModelState.AddModelError(string.Empty, "Bạn không có quyền truy cập vào trang quản trị.");
                    }
                }
                else
                {
                    ModelState.AddModelError(string.Empty, "Tên đăng nhập hoặc mật khẩu không đúng.");
                }
            }

            return View(model);
        }

        public async Task<IActionResult> Logout()
        {
            await _signInManager.SignOutAsync();
            return RedirectToAction("Login");
        }

        public async Task<IActionResult> Index()
        {
            ViewData["Title"] = "Dashboard Admin";
            
            var today = DateTime.Today;
            
            var viewModel = new AdminDashboardViewModel
            {
                TotalLaptops = await _context.Laptops.CountAsync(l => l.IsActive),
                TotalOrders = await _context.Orders.CountAsync(),
                TotalCustomers = await _context.Users.CountAsync(u => u.IsActive),
                TotalRevenue = await _context.Orders
                    .Where(o => o.Status == OrderStatus.Delivered)
                    .SumAsync(o => o.TotalAmount),
                
                TodayOrders = await _context.Orders
                    .CountAsync(o => o.CreatedAt.Date == today),
                
                TodayRevenue = await _context.Orders
                    .Where(o => o.CreatedAt.Date == today && o.Status == OrderStatus.Delivered)
                    .SumAsync(o => o.TotalAmount),
                
                RecentOrders = await _context.Orders
                    .Include(o => o.User)
                    .OrderByDescending(o => o.CreatedAt)
                    .Take(5)
                    .ToListAsync(),
                
                TopLaptops = await _context.Laptops
                    .Include(l => l.Brand)
                    .Include(l => l.Category)
                    .Where(l => l.IsActive && l.IsFeatured)
                    .OrderByDescending(l => l.IsFeatured)
                    .ThenBy(l => l.Name)
                    .Take(5)
                    .ToListAsync(),
                
                LowStockLaptops = await _context.Laptops
                    .Include(l => l.Brand)
                    .Include(l => l.Category)
                    .Where(l => l.IsActive && l.StockQuantity <= 5)
                    .OrderBy(l => l.StockQuantity)
                    .Take(5)
                    .ToListAsync()
            };

            return View(viewModel);
        }

        public async Task<IActionResult> Laptops(string? search, int? categoryId, int? brandId, int page = 1)
        {
            ViewData["Title"] = "Quản Lý Laptop";
            
            var query = _context.Laptops
                .Include(l => l.Brand)
                .Include(l => l.Category)
                .AsQueryable();

            if (!string.IsNullOrEmpty(search))
            {
                query = query.Where(l => l.Name.Contains(search) || l.Model!.Contains(search));
            }

            if (categoryId.HasValue)
            {
                query = query.Where(l => l.CategoryId == categoryId.Value);
            }

            if (brandId.HasValue)
            {
                query = query.Where(l => l.BrandId == brandId.Value);
            }

            const int pageSize = 10;
            var totalItems = await query.CountAsync();
            var totalPages = (int)Math.Ceiling(totalItems / (double)pageSize);

            var laptops = await query
                .OrderByDescending(l => l.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            ViewBag.Categories = await _context.Categories.Where(c => c.IsActive).ToListAsync();
            ViewBag.Brands = await _context.Brands.Where(b => b.IsActive).ToListAsync();
            ViewBag.Search = search;
            ViewBag.CategoryId = categoryId;
            ViewBag.BrandId = brandId;
            ViewBag.CurrentPage = page;
            ViewBag.TotalPages = totalPages;
            ViewBag.TotalItems = totalItems;

            return View(laptops);
        }

        public async Task<IActionResult> Orders(string? search, OrderStatus? status, int page = 1)
        {
            ViewData["Title"] = "Quản Lý Đơn Hàng";
            
            var query = _context.Orders
                .Include(o => o.User)
                .Include(o => o.OrderDetails)
                    .ThenInclude(od => od.Laptop)
                .AsQueryable();

            if (!string.IsNullOrEmpty(search))
            {
                query = query.Where(o => o.OrderNumber.Contains(search) || 
                                        o.CustomerName.Contains(search) ||
                                        o.CustomerEmail.Contains(search));
            }

            if (status.HasValue)
            {
                query = query.Where(o => o.Status == status.Value);
            }

            const int pageSize = 10;
            var totalItems = await query.CountAsync();
            var totalPages = (int)Math.Ceiling(totalItems / (double)pageSize);

            var orders = await query
                .OrderByDescending(o => o.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            ViewBag.Search = search;
            ViewBag.Status = status;
            ViewBag.CurrentPage = page;
            ViewBag.TotalPages = totalPages;
            ViewBag.TotalItems = totalItems;

            return View(orders);
        }

        public async Task<IActionResult> Customers(string? search, int page = 1)
        {
            ViewData["Title"] = "Quản Lý Khách Hàng";
            
            var query = _context.Users.AsQueryable();

            if (!string.IsNullOrEmpty(search))
            {
                query = query.Where(u => u.FullName.Contains(search) || 
                                        u.Email!.Contains(search) ||
                                        u.PhoneNumber!.Contains(search));
            }

            const int pageSize = 10;
            var totalItems = await query.CountAsync();
            var totalPages = (int)Math.Ceiling(totalItems / (double)pageSize);

            var customers = await query
                .OrderByDescending(u => u.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            ViewBag.Search = search;
            ViewBag.CurrentPage = page;
            ViewBag.TotalPages = totalPages;
            ViewBag.TotalItems = totalItems;

            return View(customers);
        }

        public async Task<IActionResult> Categories()
        {
            ViewData["Title"] = "Quản Lý Danh Mục";
            
            var categories = await _context.Categories
                .OrderBy(c => c.Name)
                .ToListAsync();

            return View(categories);
        }

        public async Task<IActionResult> Brands()
        {
            ViewData["Title"] = "Quản Lý Thương Hiệu";
            
            var brands = await _context.Brands
                .OrderBy(b => b.Name)
                .ToListAsync();

            return View(brands);
        }
    }
}
