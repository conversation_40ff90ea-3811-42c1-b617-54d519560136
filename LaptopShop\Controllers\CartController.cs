using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using LaptopShop.Data;
using LaptopShop.Models;

namespace LaptopShop.Controllers
{
    [Authorize]
    public class CartController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;

        public CartController(ApplicationDbContext context, UserManager<ApplicationUser> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        public async Task<IActionResult> Index()
        {
            ViewData["Title"] = "Giỏ Hàng";
            
            var userId = _userManager.GetUserId(User);
            var cartViewModel = await GetCartViewModel(userId!);
            
            return View(cartViewModel);
        }

        [HttpPost]
        public async Task<IActionResult> AddToCart(int laptopId, int quantity = 1)
        {
            var userId = _userManager.GetUserId(User);
            
            var laptop = await _context.Laptops.FindAsync(laptopId);
            if (laptop == null || !laptop.IsActive)
            {
                return Json(new { success = false, message = "Sản phẩm không tồn tại" });
            }

            if (laptop.StockQuantity < quantity)
            {
                return Json(new { success = false, message = "Không đủ hàng trong kho" });
            }

            var existingCartItem = await _context.CartItems
                .FirstOrDefaultAsync(c => c.UserId == userId && c.LaptopId == laptopId);

            if (existingCartItem != null)
            {
                var newQuantity = existingCartItem.Quantity + quantity;
                if (newQuantity > laptop.StockQuantity)
                {
                    return Json(new { success = false, message = "Không đủ hàng trong kho" });
                }
                
                existingCartItem.Quantity = newQuantity;
                existingCartItem.UpdatedAt = DateTime.Now;
            }
            else
            {
                var cartItem = new CartItem
                {
                    UserId = userId!,
                    LaptopId = laptopId,
                    Quantity = quantity
                };
                _context.CartItems.Add(cartItem);
            }

            await _context.SaveChangesAsync();

            var cartCount = await _context.CartItems
                .Where(c => c.UserId == userId)
                .SumAsync(c => c.Quantity);

            return Json(new { success = true, message = "Đã thêm vào giỏ hàng", cartCount });
        }

        [HttpPost]
        public async Task<IActionResult> UpdateQuantity(int cartItemId, int quantity)
        {
            var userId = _userManager.GetUserId(User);
            
            var cartItem = await _context.CartItems
                .Include(c => c.Laptop)
                .FirstOrDefaultAsync(c => c.Id == cartItemId && c.UserId == userId);

            if (cartItem == null)
            {
                return Json(new { success = false, message = "Không tìm thấy sản phẩm trong giỏ hàng" });
            }

            if (quantity <= 0)
            {
                _context.CartItems.Remove(cartItem);
            }
            else if (quantity > cartItem.Laptop.StockQuantity)
            {
                return Json(new { success = false, message = "Không đủ hàng trong kho" });
            }
            else
            {
                cartItem.Quantity = quantity;
                cartItem.UpdatedAt = DateTime.Now;
            }

            await _context.SaveChangesAsync();

            var cartViewModel = await GetCartViewModel(userId!);
            
            return Json(new { 
                success = true, 
                cartCount = cartViewModel.TotalItems,
                subTotal = cartViewModel.SubTotal.ToString("N0"),
                total = cartViewModel.Total.ToString("N0")
            });
        }

        [HttpPost]
        public async Task<IActionResult> RemoveItem(int cartItemId)
        {
            var userId = _userManager.GetUserId(User);
            
            var cartItem = await _context.CartItems
                .FirstOrDefaultAsync(c => c.Id == cartItemId && c.UserId == userId);

            if (cartItem != null)
            {
                _context.CartItems.Remove(cartItem);
                await _context.SaveChangesAsync();
            }

            var cartViewModel = await GetCartViewModel(userId!);
            
            return Json(new { 
                success = true, 
                cartCount = cartViewModel.TotalItems,
                subTotal = cartViewModel.SubTotal.ToString("N0"),
                total = cartViewModel.Total.ToString("N0")
            });
        }

        [HttpPost]
        public async Task<IActionResult> ClearCart()
        {
            var userId = _userManager.GetUserId(User);
            
            var cartItems = await _context.CartItems
                .Where(c => c.UserId == userId)
                .ToListAsync();

            _context.CartItems.RemoveRange(cartItems);
            await _context.SaveChangesAsync();

            return Json(new { success = true, message = "Đã xóa tất cả sản phẩm khỏi giỏ hàng" });
        }

        [HttpGet]
        public async Task<IActionResult> GetCartCount()
        {
            var userId = _userManager.GetUserId(User);
            
            var cartCount = await _context.CartItems
                .Where(c => c.UserId == userId)
                .SumAsync(c => c.Quantity);

            return Json(new { cartCount });
        }

        private async Task<CartViewModel> GetCartViewModel(string userId)
        {
            var cartItems = await _context.CartItems
                .Include(c => c.Laptop)
                    .ThenInclude(l => l.Brand)
                .Where(c => c.UserId == userId)
                .ToListAsync();

            var cartItemViewModels = cartItems.Select(c => new CartItemViewModel
            {
                Id = c.Id,
                LaptopId = c.LaptopId,
                LaptopName = c.Laptop.Name,
                LaptopImage = c.Laptop.MainImageUrl,
                Price = c.Laptop.Price,
                SalePrice = c.Laptop.SalePrice,
                Quantity = c.Quantity,
                Total = (c.Laptop.SalePrice ?? c.Laptop.Price) * c.Quantity,
                StockQuantity = c.Laptop.StockQuantity
            }).ToList();

            var subTotal = cartItemViewModels.Sum(c => c.Total);
            var shippingFee = subTotal >= 500000 ? 0 : 30000; // Miễn phí ship cho đơn hàng >= 500k

            return new CartViewModel
            {
                Items = cartItemViewModels,
                SubTotal = subTotal,
                ShippingFee = shippingFee,
                Total = subTotal + shippingFee,
                TotalItems = cartItemViewModels.Sum(c => c.Quantity)
            };
        }
    }
}
