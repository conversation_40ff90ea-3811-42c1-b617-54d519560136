// Template implementations for SortingAlgorithms class

#include <chrono>

// Utility functions
template<typename T>
void SortingAlgorithms::swap(T& a, T& b) {
    swapCount++;
    T temp = a;
    a = b;
    b = temp;
}

template<typename T>
bool SortingAlgorithms::compare(const T& a, const T& b, std::function<bool(const T&, const T&)> compareFn) {
    comparisonCount++;
    return compareFn(a, b);
}

// Measure execution time
template<typename Func>
long long SortingAlgorithms::measureTime(Func func) {
    auto start = std::chrono::high_resolution_clock::now();
    func();
    auto end = std::chrono::high_resolution_clock::now();
    return std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
}

// Bubble Sort
template<typename T>
SortingStats SortingAlgorithms::bubbleSort(std::vector<T>& arr, std::function<bool(const T&, const T&)> compareFn) {
    resetCounters();
    int n = arr.size();

    auto executionTime = measureTime([&]() {
        for (int i = 0; i < n - 1; i++) {
            bool swapped = false;
            for (int j = 0; j < n - i - 1; j++) {
                if (compare(arr[j + 1], arr[j], compareFn)) {
                    swap(arr[j], arr[j + 1]);
                    swapped = true;
                }
            }
            if (!swapped) break; // Optimization: if no swaps, array is sorted
        }
    });

    return SortingStats("Bubble Sort", executionTime, getComparisonCount(), getSwapCount(), n);
}

// Selection Sort
template<typename T>
SortingStats SortingAlgorithms::selectionSort(std::vector<T>& arr, std::function<bool(const T&, const T&)> compareFn) {
    resetCounters();
    int n = arr.size();

    auto executionTime = measureTime([&]() {
        for (int i = 0; i < n - 1; i++) {
            int minIdx = i;
            for (int j = i + 1; j < n; j++) {
                if (compare(arr[j], arr[minIdx], compareFn)) {
                    minIdx = j;
                }
            }
            if (minIdx != i) {
                swap(arr[i], arr[minIdx]);
            }
        }
    });

    return SortingStats("Selection Sort", executionTime, getComparisonCount(), getSwapCount(), n);
}

// Insertion Sort
template<typename T>
SortingStats SortingAlgorithms::insertionSort(std::vector<T>& arr, std::function<bool(const T&, const T&)> compareFn) {
    resetCounters();
    int n = arr.size();

    auto executionTime = measureTime([&]() {
        for (int i = 1; i < n; i++) {
            T key = arr[i];
            int j = i - 1;

            while (j >= 0 && compare(key, arr[j], compareFn)) {
                comparisonCount++; // Count the comparison in while condition
                arr[j + 1] = arr[j];
                swapCount++; // Count as a move operation
                j--;
            }
            if (j >= 0) comparisonCount++; // Count the final comparison that breaks the loop
            arr[j + 1] = key;
        }
    });

    return SortingStats("Insertion Sort", executionTime, getComparisonCount(), getSwapCount(), n);
}

// Quick Sort
template<typename T>
SortingStats SortingAlgorithms::quickSort(std::vector<T>& arr, std::function<bool(const T&, const T&)> compareFn) {
    resetCounters();
    int n = arr.size();

    auto executionTime = measureTime([&]() {
        if (n > 1) {
            quickSortHelper(arr, 0, n - 1, compareFn);
        }
    });

    return SortingStats("Quick Sort", executionTime, getComparisonCount(), getSwapCount(), n);
}

template<typename T>
void SortingAlgorithms::quickSortHelper(std::vector<T>& arr, int low, int high, std::function<bool(const T&, const T&)> compareFn) {
    if (low < high) {
        int pi = partition(arr, low, high, compareFn);
        quickSortHelper(arr, low, pi - 1, compareFn);
        quickSortHelper(arr, pi + 1, high, compareFn);
    }
}

template<typename T>
int SortingAlgorithms::partition(std::vector<T>& arr, int low, int high, std::function<bool(const T&, const T&)> compareFn) {
    T pivot = arr[high];
    int i = low - 1;

    for (int j = low; j < high; j++) {
        if (compare(arr[j], pivot, compareFn) || arr[j] == pivot) {
            i++;
            if (i != j) {
                swap(arr[i], arr[j]);
            }
        }
    }
    if (i + 1 != high) {
        swap(arr[i + 1], arr[high]);
    }
    return i + 1;
}

// Merge Sort
template<typename T>
SortingStats SortingAlgorithms::mergeSort(std::vector<T>& arr, std::function<bool(const T&, const T&)> compareFn) {
    resetCounters();
    int n = arr.size();

    auto executionTime = measureTime([&]() {
        if (n > 1) {
            mergeSortHelper(arr, 0, n - 1, compareFn);
        }
    });

    return SortingStats("Merge Sort", executionTime, getComparisonCount(), getSwapCount(), n);
}

template<typename T>
void SortingAlgorithms::mergeSortHelper(std::vector<T>& arr, int left, int right, std::function<bool(const T&, const T&)> compareFn) {
    if (left < right) {
        int mid = left + (right - left) / 2;
        mergeSortHelper(arr, left, mid, compareFn);
        mergeSortHelper(arr, mid + 1, right, compareFn);
        merge(arr, left, mid, right, compareFn);
    }
}

template<typename T>
void SortingAlgorithms::merge(std::vector<T>& arr, int left, int mid, int right, std::function<bool(const T&, const T&)> compareFn) {
    int n1 = mid - left + 1;
    int n2 = right - mid;

    std::vector<T> leftArr(n1), rightArr(n2);

    for (int i = 0; i < n1; i++) {
        leftArr[i] = arr[left + i];
    }
    for (int j = 0; j < n2; j++) {
        rightArr[j] = arr[mid + 1 + j];
    }

    int i = 0, j = 0, k = left;

    while (i < n1 && j < n2) {
        if (compare(leftArr[i], rightArr[j], compareFn) || leftArr[i] == rightArr[j]) {
            arr[k] = leftArr[i];
            i++;
        } else {
            arr[k] = rightArr[j];
            j++;
        }
        swapCount++; // Count as a move operation
        k++;
    }

    while (i < n1) {
        arr[k] = leftArr[i];
        swapCount++;
        i++;
        k++;
    }

    while (j < n2) {
        arr[k] = rightArr[j];
        swapCount++;
        j++;
        k++;
    }
}

// Utility functions
template<typename T>
void SortingAlgorithms::swap(T& a, T& b) {
    swapCount++;
    T temp = a;
    a = b;
    b = temp;
}

template<typename T>
bool SortingAlgorithms::compare(const T& a, const T& b, std::function<bool(const T&, const T&)> compareFn) {
    comparisonCount++;
    return compareFn(a, b);
}

// Measure execution time
template<typename Func>
long long SortingAlgorithms::measureTime(Func func) {
    auto start = std::chrono::high_resolution_clock::now();
    func();
    auto end = std::chrono::high_resolution_clock::now();
    return std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
}

// Bubble Sort
template<typename T>
SortingStats SortingAlgorithms::bubbleSort(std::vector<T>& arr, std::function<bool(const T&, const T&)> compareFn) {
    resetCounters();
    int n = arr.size();
    
    auto executionTime = measureTime([&]() {
        for (int i = 0; i < n - 1; i++) {
            bool swapped = false;
            for (int j = 0; j < n - i - 1; j++) {
                if (compare(arr[j + 1], arr[j], compareFn)) {
                    swap(arr[j], arr[j + 1]);
                    swapped = true;
                }
            }
            if (!swapped) break; // Optimization: if no swaps, array is sorted
        }
    });
    
    return SortingStats("Bubble Sort", executionTime, getComparisonCount(), getSwapCount(), n);
}

// Selection Sort
template<typename T>
SortingStats SortingAlgorithms::selectionSort(std::vector<T>& arr, std::function<bool(const T&, const T&)> compareFn) {
    resetCounters();
    int n = arr.size();
    
    auto executionTime = measureTime([&]() {
        for (int i = 0; i < n - 1; i++) {
            int minIdx = i;
            for (int j = i + 1; j < n; j++) {
                if (compare(arr[j], arr[minIdx], compareFn)) {
                    minIdx = j;
                }
            }
            if (minIdx != i) {
                swap(arr[i], arr[minIdx]);
            }
        }
    });
    
    return SortingStats("Selection Sort", executionTime, getComparisonCount(), getSwapCount(), n);
}

// Insertion Sort
template<typename T>
SortingStats SortingAlgorithms::insertionSort(std::vector<T>& arr, std::function<bool(const T&, const T&)> compareFn) {
    resetCounters();
    int n = arr.size();
    
    auto executionTime = measureTime([&]() {
        for (int i = 1; i < n; i++) {
            T key = arr[i];
            int j = i - 1;
            
            while (j >= 0 && compare(key, arr[j], compareFn)) {
                comparisonCount++; // Count the comparison in while condition
                arr[j + 1] = arr[j];
                swapCount++; // Count as a move operation
                j--;
            }
            if (j >= 0) comparisonCount++; // Count the final comparison that breaks the loop
            arr[j + 1] = key;
        }
    });
    
    return SortingStats("Insertion Sort", executionTime, getComparisonCount(), getSwapCount(), n);
}

// Quick Sort
template<typename T>
SortingStats SortingAlgorithms::quickSort(std::vector<T>& arr, std::function<bool(const T&, const T&)> compareFn) {
    resetCounters();
    int n = arr.size();
    
    auto executionTime = measureTime([&]() {
        if (n > 1) {
            quickSortHelper(arr, 0, n - 1, compareFn);
        }
    });
    
    return SortingStats("Quick Sort", executionTime, getComparisonCount(), getSwapCount(), n);
}

template<typename T>
void SortingAlgorithms::quickSortHelper(std::vector<T>& arr, int low, int high, std::function<bool(const T&, const T&)> compareFn) {
    if (low < high) {
        int pi = partition(arr, low, high, compareFn);
        quickSortHelper(arr, low, pi - 1, compareFn);
        quickSortHelper(arr, pi + 1, high, compareFn);
    }
}

template<typename T>
int SortingAlgorithms::partition(std::vector<T>& arr, int low, int high, std::function<bool(const T&, const T&)> compareFn) {
    T pivot = arr[high];
    int i = low - 1;
    
    for (int j = low; j < high; j++) {
        if (compare(arr[j], pivot, compareFn) || arr[j] == pivot) {
            i++;
            if (i != j) {
                swap(arr[i], arr[j]);
            }
        }
    }
    if (i + 1 != high) {
        swap(arr[i + 1], arr[high]);
    }
    return i + 1;
}

// Merge Sort
template<typename T>
SortingStats SortingAlgorithms::mergeSort(std::vector<T>& arr, std::function<bool(const T&, const T&)> compareFn) {
    resetCounters();
    int n = arr.size();
    
    auto executionTime = measureTime([&]() {
        if (n > 1) {
            mergeSortHelper(arr, 0, n - 1, compareFn);
        }
    });
    
    return SortingStats("Merge Sort", executionTime, getComparisonCount(), getSwapCount(), n);
}

template<typename T>
void SortingAlgorithms::mergeSortHelper(std::vector<T>& arr, int left, int right, std::function<bool(const T&, const T&)> compareFn) {
    if (left < right) {
        int mid = left + (right - left) / 2;
        mergeSortHelper(arr, left, mid, compareFn);
        mergeSortHelper(arr, mid + 1, right, compareFn);
        merge(arr, left, mid, right, compareFn);
    }
}

template<typename T>
void SortingAlgorithms::merge(std::vector<T>& arr, int left, int mid, int right, std::function<bool(const T&, const T&)> compareFn) {
    int n1 = mid - left + 1;
    int n2 = right - mid;
    
    std::vector<T> leftArr(n1), rightArr(n2);
    
    for (int i = 0; i < n1; i++) {
        leftArr[i] = arr[left + i];
    }
    for (int j = 0; j < n2; j++) {
        rightArr[j] = arr[mid + 1 + j];
    }
    
    int i = 0, j = 0, k = left;
    
    while (i < n1 && j < n2) {
        if (compare(leftArr[i], rightArr[j], compareFn) || leftArr[i] == rightArr[j]) {
            arr[k] = leftArr[i];
            i++;
        } else {
            arr[k] = rightArr[j];
            j++;
        }
        swapCount++; // Count as a move operation
        k++;
    }
    
    while (i < n1) {
        arr[k] = leftArr[i];
        swapCount++;
        i++;
        k++;
    }
    
    while (j < n2) {
        arr[k] = rightArr[j];
        swapCount++;
        j++;
        k++;
    }
}
