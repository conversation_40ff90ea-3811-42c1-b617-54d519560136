﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace LaptopShop.Migrations
{
    /// <inheritdoc />
    public partial class SeedLaptopData : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "Laptops",
                columns: new[] { "Id", "BatteryLife", "BrandId", "CategoryId", "Color", "Connectivity", "CreatedAt", "Description", "GraphicsCard", "ImageUrls", "IsActive", "IsFeatured", "MainImageUrl", "Model", "Name", "OperatingSystem", "Price", "Processor", "RAM", "Resolution", "SalePrice", "ScreenSize", "StockQuantity", "Storage", "UpdatedAt", "Weight" },
                values: new object[,]
                {
                    { 1, "6-8 giờ", 1, 1, "<PERSON><PERSON>", "WiFi 6, Bluetooth 5.1, USB-C, HDMI", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Laptop gaming mạnh mẽ với AMD Ryzen 7 và RTX 3060", "NVIDIA RTX 3060 6GB", null, true, true, null, "G513QM", "ASUS ROG Strix G15", "Windows 11", 25990000m, "AMD Ryzen 7 5800H", "16GB DDR4", "1920x1080 144Hz", 23990000m, "15.6 inch", 15, "512GB SSD NVMe", null, "2.3kg" },
                    { 2, "10-12 giờ", 2, 4, "Bạc", "WiFi 6E, Bluetooth 5.2, USB-C Thunderbolt 4", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Laptop mỏng nhẹ cao cấp cho doanh nhân", "Intel Iris Xe", null, true, true, null, "9320", "Dell XPS 13", "Windows 11", 32990000m, "Intel Core i7-1260P", "16GB LPDDR5", "1920x1200", 29990000m, "13.4 inch", 10, "512GB SSD NVMe", null, "1.27kg" },
                    { 3, "5-7 giờ", 3, 1, "Xanh đen", "WiFi 5, Bluetooth 5.0, USB-A, HDMI", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Laptop gaming giá rẻ hiệu năng tốt", "NVIDIA GTX 1650 4GB", null, true, false, null, "15-dk2000", "HP Pavilion Gaming 15", "Windows 11", 18990000m, "Intel Core i5-11300H", "8GB DDR4", "1920x1080", 16990000m, "15.6 inch", 20, "512GB SSD", null, "2.23kg" },
                    { 4, "8-10 giờ", 4, 2, "Đen", "WiFi 6, Bluetooth 5.1, USB-A, USB-C, HDMI", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Laptop văn phòng bền bỉ cho doanh nghiệp", "Intel Iris Xe", null, true, false, null, "Gen 4", "Lenovo ThinkPad E15", "Windows 11 Pro", 21990000m, "Intel Core i5-1235U", "8GB DDR4", "1920x1080", null, "15.6 inch", 12, "256GB SSD", null, "1.7kg" },
                    { 5, "7-9 giờ", 5, 2, "Bạc", "WiFi 6, Bluetooth 5.1, USB-A, USB-C, HDMI", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Laptop học tập và làm việc giá tốt", "Intel Iris Xe", null, true, true, null, "A515-57", "Acer Aspire 5", "Windows 11", 14990000m, "Intel Core i5-1235U", "8GB DDR4", "1920x1080", 13990000m, "15.6 inch", 25, "512GB SSD", null, "1.7kg" },
                    { 6, "5-7 giờ", 6, 1, "Đen", "WiFi 6, Bluetooth 5.1, USB-A, USB-C, HDMI", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Laptop gaming mỏng nhẹ với GTX 1650", "NVIDIA GTX 1650 4GB", null, true, false, null, "Thin 11UC", "MSI Gaming GF63", "Windows 11", 19990000m, "Intel Core i5-11400H", "8GB DDR4", "1920x1080", 17990000m, "15.6 inch", 18, "512GB SSD", null, "1.86kg" },
                    { 7, "15-18 giờ", 7, 4, "Bạc", "WiFi 6, Bluetooth 5.0, USB-C Thunderbolt", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Laptop Apple với chip M2 mạnh mẽ và tiết kiệm pin", "Apple M2 8-core GPU", null, true, true, null, "2022", "MacBook Air M2", "macOS", 32990000m, "Apple M2 8-core", "8GB Unified Memory", "2560x1664 Retina", 29990000m, "13.6 inch", 8, "256GB SSD", null, "1.24kg" },
                    { 8, "6-8 giờ", 1, 2, "Xanh", "WiFi 5, Bluetooth 4.2, USB-A, HDMI", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Laptop văn phòng giá rẻ hiệu năng ổn định", "Intel UHD Graphics", null, true, false, null, "X1500EA", "ASUS VivoBook 15", "Windows 11", 12990000m, "Intel Core i3-1115G4", "4GB DDR4", "1920x1080", 11990000m, "15.6 inch", 30, "256GB SSD", null, "1.8kg" },
                    { 9, "5-7 giờ", 2, 2, "Đen", "WiFi 5, Bluetooth 5.0, USB-A, HDMI", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Laptop cơ bản cho học sinh sinh viên", "Intel UHD Graphics", null, true, false, null, "3511", "Dell Inspiron 15 3000", "Windows 11", 11990000m, "Intel Core i3-1115G4", "4GB DDR4", "1366x768", null, "15.6 inch", 22, "256GB SSD", null, "1.83kg" },
                    { 10, "8-10 giờ", 3, 3, "Xám", "WiFi 6E, Bluetooth 5.2, USB-C Thunderbolt 4", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Laptop workstation chuyên nghiệp cho thiết kế đồ họa", "NVIDIA RTX A2000 4GB", null, true, true, null, "Mobile Workstation", "HP ZBook Studio G8", "Windows 11 Pro", 45990000m, "Intel Core i7-11800H", "32GB DDR4", "3840x2160 4K", 42990000m, "15.6 inch", 5, "1TB SSD NVMe", null, "1.79kg" }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 3);

            migrationBuilder.DeleteData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 4);

            migrationBuilder.DeleteData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 5);

            migrationBuilder.DeleteData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 6);

            migrationBuilder.DeleteData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 7);

            migrationBuilder.DeleteData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 8);

            migrationBuilder.DeleteData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 9);

            migrationBuilder.DeleteData(
                table: "Laptops",
                keyColumn: "Id",
                keyValue: 10);
        }
    }
}
