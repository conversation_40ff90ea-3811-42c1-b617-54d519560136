/* Admin Panel Styles */
:root {
    --admin-primary: #6c63ff;
    --admin-secondary: #f8f9fa;
    --admin-dark: #2c3e50;
    --admin-light: #ffffff;
    --admin-border: #e9ecef;
    --admin-success: #28a745;
    --admin-warning: #ffc107;
    --admin-danger: #dc3545;
    --admin-info: #17a2b8;
    --sidebar-width: 260px;
    --header-height: 70px;
}

.admin-body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--admin-secondary);
    margin: 0;
    padding: 0;
}

.admin-wrapper {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Styles */
.admin-sidebar {
    width: var(--sidebar-width);
    background: linear-gradient(135deg, var(--admin-primary) 0%, #5a52d5 100%);
    color: white;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    transition: all 0.3s ease;
}

.sidebar-header {
    padding: 1.5rem;
    text-align: center;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar-header h4 {
    margin: 0;
    font-weight: 700;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 1rem 0;
}

.menu-item {
    margin: 0.5rem 0;
}

.menu-link {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.menu-link:hover,
.menu-link.active {
    background-color: rgba(255,255,255,0.1);
    color: white;
    border-left-color: white;
}

.menu-link i {
    width: 20px;
    margin-right: 1rem;
    text-align: center;
}

/* Main Content */
.admin-main {
    flex: 1;
    margin-left: var(--sidebar-width);
    display: flex;
    flex-direction: column;
}

/* Header */
.admin-header {
    height: var(--header-height);
    background: var(--admin-light);
    border-bottom: 1px solid var(--admin-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 2rem;
    position: sticky;
    top: 0;
    z-index: 999;
}

.sidebar-toggle {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--admin-dark);
    cursor: pointer;
}

.admin-user {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

/* Content Area */
.admin-content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

/* Dashboard Styles */
.dashboard-header {
    margin-bottom: 2rem;
}

.dashboard-header h1 {
    color: var(--admin-dark);
    margin-bottom: 0.5rem;
}

.stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--admin-light);
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-right: 1rem;
}

.stat-card:nth-child(1) .stat-icon {
    background: linear-gradient(135deg, var(--admin-info), #0ea5e9);
    color: white;
}

.stat-card:nth-child(2) .stat-icon {
    background: linear-gradient(135deg, var(--admin-success), #22c55e);
    color: white;
}

.stat-card:nth-child(3) .stat-icon {
    background: linear-gradient(135deg, var(--admin-warning), #f59e0b);
    color: white;
}

.stat-card:nth-child(4) .stat-icon {
    background: linear-gradient(135deg, var(--admin-danger), #ef4444);
    color: white;
}

.stat-content h3 {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
    color: var(--admin-dark);
}

.stat-content p {
    margin: 0;
    color: #6b7280;
    font-size: 0.9rem;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
}

.card-header {
    background: var(--admin-light);
    border-bottom: 1px solid var(--admin-border);
    padding: 1rem 1.5rem;
    border-radius: 12px 12px 0 0 !important;
}

.card-header h5 {
    margin: 0;
    color: var(--admin-dark);
    font-weight: 600;
}

/* Recent Orders */
.recent-order {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--admin-border);
}

.recent-order:last-child {
    border-bottom: none;
}

.order-info strong {
    color: var(--admin-dark);
}

.order-time {
    color: #6b7280;
    font-size: 0.85rem;
    margin-left: 0.5rem;
}

.order-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.order-status.pending {
    background: rgba(255, 193, 7, 0.1);
    color: var(--admin-warning);
}

.order-status.completed {
    background: rgba(40, 167, 69, 0.1);
    color: var(--admin-success);
}

.order-status.preparing {
    background: rgba(23, 162, 184, 0.1);
    color: var(--admin-info);
}

/* Responsive */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
    }
    
    .admin-main {
        margin-left: 0;
    }
    
    .stats-overview {
        grid-template-columns: 1fr;
    }
    
    .admin-content {
        padding: 1rem;
    }
}
