{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["EGFOH1Jgxqau47FDxJhp7GlrsO7rXGU24W+zIz1Ax74=", "mv8BULzr+kmt6rFl+lcKMTzKkKdLST0m08f0eIGLChY=", "yAHfdZL4J/Do76Er/T857PwtSbyPy4r3L23VQBfCFO8=", "ZXgkiaBFF3HCXLNENb+p/9zDOoAWoBOVOaey8Lgg7rU=", "rjBYztVcgOJ4RLpZdLgchc5xHvlSBwu1+MFhRMTmxEk=", "dIlC2CrAIKli1aB7+Fh75K6B+JN4Mix6RcZxTYciI9I=", "T8N+YJoUeWv7hKEqs1fvIO5ruwfDdkAXmvLq2J0hC28=", "JGmkVEPvdDkc6r/Gwsewc+NnWBa5IUxtf0Z7y/W7GX4=", "KzMxcWOAHKzhf6yRD5GII57V/3zFHpQ0HxRJvJ+/cFg=", "W3rOm4zAH9PI0V+nT0pYHMNIp/2wBx94vaWLBiFn5fI=", "XRyHZBjDuHfWYDuJ9UgMI2O1JEe2SY5VYGoPs+mZ2Vw=", "zlu9qpL+sddacDEO1GaCtecgYn1OXERNjSew/QUt3vY=", "VFFCpuKnghC0NipfP1p/SkpVPY2rkECNTN4w27WVFYg=", "Mh7hX6vvdnITe04SR8k5AZ7OW8lFzE3PnMuOvw9umFw=", "CB/XUzDh4dms8vELZfxR7a7Lzqs0vpNAHTfEGwwGPgU=", "uKNHn1duOC2Q8Hcprhzt0e7iWadtdnAWeJ8fwCoj9B0=", "14KdDi2OLskSK8Kn2r3+kdit8OEzIUrKFOAJX5X69Qo=", "mWLlfjU0yYyKAzi4n1S2jOUw4yfv+6Br/cRLT4G/WZQ=", "lBDvfXGSy6W06yQgWaD7omIyATQ2sdgDFpWzove24+Y=", "I1YVD9JEZVGAXRJlLMVGheEftSzeDqGC0VflBbu7fKE=", "Pps0YDfb6rKcebGnpsebkloyKqJ6I9oAMNvDG1pUQys=", "NrX9BzarYZ6XKUeQYwQQo3prkGUHgKwk4sKJ7p5KdMk=", "y149vc9D1p9ylwepi8orZk62JzmTW8il94oOkIT4n1Y=", "SkEDH9nWXlj/x7woSeYBrnYE0wUvAN92/nVg7iUo8wE=", "XutRjI/O3O+ODKdxLYnEOoA3oPkfUPEazQZdOiea5Y8=", "fVpeTjLxBjFy4e8nL4z6obfnXSy3nFpCVUF1+NuwwTs=", "TrQOJcPug+qYoR1vxBPlAQfhenHefFo82NyC7/6Pl90=", "h1IdsaWNfiHESELoGcK1qorSDU46X4RS0/bbvEDkIFM=", "n2+v3gzapg1s8Gy3dDXqq9HosNSwawteo/CogZuInTw=", "keyeTHGgZelbRA6P/gVJHwlPtjUDnx3QUGO22VJWv54=", "yx5sQ4h3yO7vdrkTSk3SfW8CGjXbo2vO4xmSx2xSYGI=", "FOPp0uKqujkB5TSRFtoTpiNAQ8FdpdwVc9blMdbWYQ4=", "vj+p7FxlL32dgjGjGduHuyU3z9fhAWGzE8yhit6Zg6I=", "+2qyHBsjlBccVdFA5IbBoGjGBJK4iTUvXMYhH/LiSSk=", "hEE39Uvb0wt9e1k8vbfz2rq2IEvTkpfSkDsqpO87F9M=", "xw5L6I9r8WLGWEbWxhCQl5SVDLODfyCVH6L4PU72Sbc=", "MyxWbm9ryhYcHow2tlejxl6kpmojfv8Y1KRuZjRrSS0=", "NBXiwc++2TIzLUGX4KXTW/TDc5oKgAS3ZNvfUrJZ85E=", "6pULLnOQJkvkj/Qhxu2xTpvWTMUADrkiTSrLTrhPQu8=", "JIijWVIzzNz2jw1fCqSEs0O2XOdJrg7x2FysPy3nmjw=", "lRLvwJrJURFkK8nmMVHSqcdy2TdnYiL8Fjnip0ONezM=", "ULmtya5RlEPAeSEqyDDN0/uCUzgt2BcK7q6/wJPiq74=", "qi69v5MWYLYgrHHS1uB6D1mXj4zrkZ9K566btcwoA6Q=", "ddezbRyjmeiiKCdAghqdPJr9q4HjZRfaEFN3CFQ1ihQ=", "yxPW+1GiAhLE7FDw+l7MN3EaajCI9mEYv1YUGf3aeeg=", "M7OWzcW/5mZrioO2H5EkH/u1tPyMKy2uHH1Wzxu+Su8=", "lpLmYvwCQDN9np92izTvwkVwJSO6cicKsxnexv0bRbg=", "pOlpNctFLudGiy0xXHKlv8uCAqMTRceszYNkqi9MvVo=", "OHSBWts2fvCeO6apr604QC5mpjw3AsNLDEXWxEhQBgY=", "BvYuQRXWDpRQ9rjDvAD70EhA3JjWiNFwATfCW6tLFrg=", "WD9BTztrc5pnIm7XeCJfsMHYD0Q8oBYc3h2XooAgnHs=", "v3bLlxoeEZamdMWXtCWxt75h7LqgCNwnByRSH7bxiIk=", "/1gk9Oq6xG93QzHfnlXMXUxala0LpM0rDjEa9ZuknNU=", "vAdbEkgWdwZKqwfPujlhryuuUWU0gmE7TXNKqXW/9+8=", "KCm4NNQKqGXlAD7knqLs4UkvARW/Vt3j+83ebd2XyWQ=", "+SfGWgZ/KNVw/bHgObCbMct/6ZCBmGJPZfyRuvZPpdM=", "yxhCsD37taMgdXKQ8o5JFMI9odjIiSqb34DIzlqxhwA=", "eduNsPXWsEsjfQgBCbevfdLtLtDVE6DeQHpwniY4Upg=", "X70ccg37xAf3H+T9/a0alDWroTMhKAp4/dmfPCyVBR0=", "F8WmoogWK6vttbLI7cL+aXfz9U+LSl9y7WQKbKcFIII=", "oyLVRMhlK4ICC20xrJJKBZTSuYghSqNWnlU2L0TXRkU=", "54rsR3ixPuy97KTVOPDTDoWQ79IK74wWJHUNgcRM9Ug=", "om8+GiO1iWLxNR9lPK4jTyzJZqBhclNb/sbyMmqBRMs=", "rNVky69f/u+ZsJSPpFCwP9Sicg6hm8VqYybSSLzXa5c=", "F+Qve9ieo2qmDH/6B8CH/B9yyXnzzsDa+DS2xeqHDVw=", "rjtUgPuQD56LmLNXoi4LAMLsyVcNtnwZu9vJ8D4+LiE=", "//ARUJVKDQdWHq3sfXUXueZWCEYFwBeaZjjMPW+BVDQ=", "ItaFmuMywdIQY0YhO70cdjJpH298j83llhjNhvMYVFo=", "GS6zkE3TLOSi0lOnYdie44R7WPNQXbK2k/8+d7xuIYc=", "buXI5ZFYxT1D4jWGWmLvWYVluT629iMikPJ90LAM0ls=", "2nL4iY+lWyYJHSM7GwAmC0jiKymh6SaC9eqPeU5WYCI=", "DBb9UT+svJ2jEzj8TY4acizE0r6sV3tWh0yS0YKqWTQ=", "W64WrNj3wHrWhZvK2Mt5lSyM7uHXgBCO/FxE4OFf6SE=", "4+V+8gfVIOo5MYDXGqjwzGxq3roXCy2D5weB4mDi4RM=", "OGNpaJwmtLau2FqteKSZnpOoXoZswbe0TBro+08rJXA="], "CachedAssets": {"yAHfdZL4J/Do76Er/T857PwtSbyPy4r3L23VQBfCFO8=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\5bmbc543ss-61n19gt1b8.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "mv8BULzr+kmt6rFl+lcKMTzKkKdLST0m08f0eIGLChY=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\fwweffc7qb-pnv3bi48ln.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "css/site#[.{fingerprint=pnv3bi48ln}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "im6wzj7h1r", "Integrity": "djdFwKs6rQWiBXBWVoi+upuTIaaiEnkDK36mLr1+jiw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\css\\site.css", "FileLength": 4080, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "EGFOH1Jgxqau47FDxJhp7GlrsO7rXGU24W+zIz1Ax74=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\uimg47dni9-m2s80dm3u7.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "css/admin#[.{fingerprint=m2s80dm3u7}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\css\\admin.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "df6fzqdxyh", "Integrity": "SYmwrW7Q9X1NGa+gYHdUZc93mhVMGw3CtYB45fnhwWU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\css\\admin.css", "FileLength": 1514, "LastWriteTime": "2025-07-15T15:42:14.1772573+00:00"}, "ZXgkiaBFF3HCXLNENb+p/9zDOoAWoBOVOaey8Lgg7rU=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\5r7nq7tv97-0khjbx7955.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "images/ca-phe-den-da#[.{fingerprint=0khjbx7955}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\images\\ca-phe-den-da.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m96qjebs2v", "Integrity": "xsJeDdK3Kqiejheq1m5gGtkJGsaMjyHsW/txdj5FN0U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\images\\ca-phe-den-da.svg", "FileLength": 959, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "rjBYztVcgOJ4RLpZdLgchc5xHvlSBwu1+MFhRMTmxEk=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\i1h0duwcfm-j5wy8axj4k.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "images/ca-phe-sua-da#[.{fingerprint=j5wy8axj4k}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\images\\ca-phe-sua-da.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j626gijvzn", "Integrity": "i9i0r3j6evAs1cC8eNUAnZoHv9KvXUUBU4rvWkvwg/Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\images\\ca-phe-sua-da.svg", "FileLength": 915, "LastWriteTime": "2025-07-15T15:42:14.1926582+00:00"}, "dIlC2CrAIKli1aB7+Fh75K6B+JN4Mix6RcZxTYciI9I=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\p63nehuhnn-5ubxltd50m.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "images/create-placeholder#[.{fingerprint=5ubxltd50m}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\images\\create-placeholder.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nsqcqd9d5x", "Integrity": "WW+43Gd/xaLkcuwEIEnBsyK89ZBMtnVn/2MXFi2I8Zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\images\\create-placeholder.html", "FileLength": 1222, "LastWriteTime": "2025-07-15T15:42:14.1926582+00:00"}, "T8N+YJoUeWv7hKEqs1fvIO5ruwfDdkAXmvLq2J0hC28=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\11yvbuu1qs-olv5dlnzif.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "images/generate-images#[.{fingerprint=olv5dlnzif}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\images\\generate-images.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ldbqdpeeev", "Integrity": "GU3eUoI8AqYfSOM1ax4tz26nYjsRjwJE2m0weRZcLpM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\images\\generate-images.html", "FileLength": 2587, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "JGmkVEPvdDkc6r/Gwsewc+NnWBa5IUxtf0Z7y/W7GX4=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\0a2y8t2h0k-m1gorpkqn2.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "images/hero-bg#[.{fingerprint=m1gorpkqn2}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\images\\hero-bg.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mbny2juk2k", "Integrity": "bgc3gyJ1DqGawW8c9udkSz+rl2RcnODYhOz47Caw/do=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\images\\hero-bg.svg", "FileLength": 1086, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "KzMxcWOAHKzhf6yRD5GII57V/3zFHpQ0HxRJvJ+/cFg=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\1uwp0j9la4-xkp057e7i2.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "images/sinh-to-xoai#[.{fingerprint=xkp057e7i2}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\images\\sinh-to-xoai.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gpkhf8zx85", "Integrity": "7H18rZJsxYTND22Z5ZoTcVOj4JbTg0DUxxlShsulFfY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\images\\sinh-to-xoai.svg", "FileLength": 940, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "W3rOm4zAH9PI0V+nT0pYHMNIp/2wBx94vaWLBiFn5fI=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\vso69jcph2-1crswvtss8.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "images/tra-dao#[.{fingerprint=1crswvtss8}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\images\\tra-dao.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2gxai6o7z", "Integrity": "nAE74zB49sUlzg2GDlV3dlRQCRw2ChI1tyKbng5v/E0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\images\\tra-dao.svg", "FileLength": 971, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "XRyHZBjDuHfWYDuJ9UgMI2O1JEe2SY5VYGoPs+mZ2Vw=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\d00ua4xmgd-pyeb9rszee.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "images/tra-sua-matcha#[.{fingerprint=pyeb9rszee}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\images\\tra-sua-matcha.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ov0jus7p8s", "Integrity": "zPYB46wwwsOpyaWD2lFVsF7kqK8dVHOfhWOgEFQuvtY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\images\\tra-sua-matcha.svg", "FileLength": 876, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "zlu9qpL+sddacDEO1GaCtecgYn1OXERNjSew/QUt3vY=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\9wvz2pkw4k-voiw205evl.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "images/tra-sua-truyen-thong#[.{fingerprint=voiw205evl}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\images\\tra-sua-truyen-thong.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "602e8c1317", "Integrity": "WnYCHi4zeHiThS4h/J0Xj+gqdIQ06+tFy5iEJjg8mNE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\images\\tra-sua-truyen-thong.svg", "FileLength": 806, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "VFFCpuKnghC0NipfP1p/SkpVPY2rkECNTN4w27WVFYg=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\a2ctd9sjin-g5tq2i8okj.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "js/admin#[.{fingerprint=g5tq2i8okj}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\js\\admin.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0ph27o1ih8", "Integrity": "FB219H7KKekuOxMocznzKcNYjog3WK5JKAju9SVM48k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\js\\admin.js", "FileLength": 482, "LastWriteTime": "2025-07-15T15:42:14.1772573+00:00"}, "Mh7hX6vvdnITe04SR8k5AZ7OW8lFzE3PnMuOvw9umFw=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\v35wrnwx99-r1ynguq8uq.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "js/site#[.{fingerprint=r1ynguq8uq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nmt4lgbs93", "Integrity": "AWxeEZnTP5jQ5za9zdwK41NgYktHgen1GZPcVe9PbJs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\js\\site.js", "FileLength": 2149, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "CB/XUzDh4dms8vELZfxR7a7Lzqs0vpNAHTfEGwwGPgU=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\5vttk2anq0-bqjiyaj88i.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "uKNHn1duOC2Q8Hcprhzt0e7iWadtdnAWeJ8fwCoj9B0=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\kh9j8y9nl7-c2jlpeoesf.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "14KdDi2OLskSK8Kn2r3+kdit8OEzIUrKFOAJX5X69Qo=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\88h1kuz114-erw9l3u2r3.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "mWLlfjU0yYyKAzi4n1S2jOUw4yfv+6Br/cRLT4G/WZQ=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\ij9l7enzha-aexeepp0ev.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-15T15:42:14.1917422+00:00"}, "lBDvfXGSy6W06yQgWaD7omIyATQ2sdgDFpWzove24+Y=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\mrxn50kmib-d7shbmvgxk.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "I1YVD9JEZVGAXRJlLMVGheEftSzeDqGC0VflBbu7fKE=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\pa3433fubv-ausgxo2sd3.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "Pps0YDfb6rKcebGnpsebkloyKqJ6I9oAMNvDG1pUQys=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\hhpxls5bgu-k8d9w2qqmf.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-15T15:42:14.1926582+00:00"}, "NrX9BzarYZ6XKUeQYwQQo3prkGUHgKwk4sKJ7p5KdMk=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\rd4oh1zqnu-cosvhxvwiu.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "y149vc9D1p9ylwepi8orZk62JzmTW8il94oOkIT4n1Y=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\vdqrd19w0a-ub07r2b239.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "SkEDH9nWXlj/x7woSeYBrnYE0wUvAN92/nVg7iUo8wE=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\fcdinv1xt2-fvhpjtyr6v.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-15T15:42:14.1917422+00:00"}, "XutRjI/O3O+ODKdxLYnEOoA3oPkfUPEazQZdOiea5Y8=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\knsq7nxox0-b7pk76d08c.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-15T15:42:14.1772573+00:00"}, "fVpeTjLxBjFy4e8nL4z6obfnXSy3nFpCVUF1+NuwwTs=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\b9n9ymbcqz-fsbi9cje9m.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "TrQOJcPug+qYoR1vxBPlAQfhenHefFo82NyC7/6Pl90=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\56b7atboyg-rzd6atqjts.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "h1IdsaWNfiHESELoGcK1qorSDU46X4RS0/bbvEDkIFM=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\3us9vicbnq-ee0r1s7dh0.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "n2+v3gzapg1s8Gy3dDXqq9HosNSwawteo/CogZuInTw=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\qdgkqeizi4-dxx9fxp4il.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "keyeTHGgZelbRA6P/gVJHwlPtjUDnx3QUGO22VJWv54=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\cqpuynuli7-jd9uben2k1.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-15T15:42:14.1917422+00:00"}, "yx5sQ4h3yO7vdrkTSk3SfW8CGjXbo2vO4xmSx2xSYGI=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\35m7m3uc1w-khv3u5hwcm.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "FOPp0uKqujkB5TSRFtoTpiNAQ8FdpdwVc9blMdbWYQ4=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\x40n5sz1n0-r4e9w2rdcm.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "vj+p7FxlL32dgjGjGduHuyU3z9fhAWGzE8yhit6Zg6I=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\qjrkyr2pb2-lcd1t2u6c8.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-15T15:42:14.220122+00:00"}, "+2qyHBsjlBccVdFA5IbBoGjGBJK4iTUvXMYhH/LiSSk=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\70dld4qim8-c2oey78nd0.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "hEE39Uvb0wt9e1k8vbfz2rq2IEvTkpfSkDsqpO87F9M=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\r7k9sbuy6k-tdbxkamptv.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "xw5L6I9r8WLGWEbWxhCQl5SVDLODfyCVH6L4PU72Sbc=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\f591s255p1-j5mq2jizvt.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-15T15:42:14.1926582+00:00"}, "MyxWbm9ryhYcHow2tlejxl6kpmojfv8Y1KRuZjRrSS0=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\t0ro4obfkw-06098lyss8.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "NBXiwc++2TIzLUGX4KXTW/TDc5oKgAS3ZNvfUrJZ85E=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\b3bes3pkpo-nvvlpmu67g.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "6pULLnOQJkvkj/Qhxu2xTpvWTMUADrkiTSrLTrhPQu8=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\40i7q8el12-s35ty4nyc5.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "JIijWVIzzNz2jw1fCqSEs0O2XOdJrg7x2FysPy3nmjw=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\fi73e751u1-pj5nd1wqec.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-15T15:42:14.2066968+00:00"}, "lRLvwJrJURFkK8nmMVHSqcdy2TdnYiL8Fjnip0ONezM=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\aql27m497f-46ein0sx1k.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-15T15:42:14.2066968+00:00"}, "ULmtya5RlEPAeSEqyDDN0/uCUzgt2BcK7q6/wJPiq74=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\bd17nyshu2-v0zj4ognzu.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-15T15:42:14.2239604+00:00"}, "qi69v5MWYLYgrHHS1uB6D1mXj4zrkZ9K566btcwoA6Q=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\ybt304pxgv-37tfw0ft22.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "ddezbRyjmeiiKCdAghqdPJr9q4HjZRfaEFN3CFQ1ihQ=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\hsdz18hyfw-hrwsygsryq.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-15T15:42:14.1926582+00:00"}, "yxPW+1GiAhLE7FDw+l7MN3EaajCI9mEYv1YUGf3aeeg=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\6stwktu1jn-pk9g2wxc8p.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-15T15:42:14.1926582+00:00"}, "M7OWzcW/5mZrioO2H5EkH/u1tPyMKy2uHH1Wzxu+Su8=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\x285v136rd-ft3s53vfgj.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-15T15:42:14.1994747+00:00"}, "lpLmYvwCQDN9np92izTvwkVwJSO6cicKsxnexv0bRbg=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\00utq5pbx5-6cfz1n2cew.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-15T15:42:14.2066968+00:00"}, "pOlpNctFLudGiy0xXHKlv8uCAqMTRceszYNkqi9MvVo=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\mt3029ipch-6pdc2jztkx.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-15T15:42:14.2213269+00:00"}, "OHSBWts2fvCeO6apr604QC5mpjw3AsNLDEXWxEhQBgY=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\jhqw0lpnj2-493y06b0oq.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "BvYuQRXWDpRQ9rjDvAD70EhA3JjWiNFwATfCW6tLFrg=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\1wj3uelf82-iovd86k7lj.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "WD9BTztrc5pnIm7XeCJfsMHYD0Q8oBYc3h2XooAgnHs=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\tifeqo7r1c-vr1egmr9el.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-15T15:42:14.2213269+00:00"}, "v3bLlxoeEZamdMWXtCWxt75h7LqgCNwnByRSH7bxiIk=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\1hr30lodgv-kbrnm935zg.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-15T15:42:14.1980629+00:00"}, "/1gk9Oq6xG93QzHfnlXMXUxala0LpM0rDjEa9ZuknNU=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\fwtcigau4f-jj8uyg4cgr.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-15T15:42:14.1994747+00:00"}, "vAdbEkgWdwZKqwfPujlhryuuUWU0gmE7TXNKqXW/9+8=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\75549f5oyv-y7v9cxd14o.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-15T15:42:14.204736+00:00"}, "KCm4NNQKqGXlAD7knqLs4UkvARW/Vt3j+83ebd2XyWQ=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\cxutck8z9s-notf2xhcfb.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "+SfGWgZ/KNVw/bHgObCbMct/6ZCBmGJPZfyRuvZPpdM=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\ke9hsozesr-h1s4sie4z3.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "yxhCsD37taMgdXKQ8o5JFMI9odjIiSqb34DIzlqxhwA=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\kkyk6k0oxb-63fj8s7r0e.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-15T15:42:14.1917422+00:00"}, "eduNsPXWsEsjfQgBCbevfdLtLtDVE6DeQHpwniY4Upg=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\jnlh7sx20q-0j3bgjxly4.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-15T15:42:14.1926582+00:00"}, "X70ccg37xAf3H+T9/a0alDWroTMhKAp4/dmfPCyVBR0=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\1oeal7qqkb-47otxtyo56.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-07-15T15:42:14.1926582+00:00"}, "F8WmoogWK6vttbLI7cL+aXfz9U+LSl9y7WQKbKcFIII=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\doy9x1eoj7-4v8eqarkd7.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-07-15T15:42:14.1980629+00:00"}, "oyLVRMhlK4ICC20xrJJKBZTSuYghSqNWnlU2L0TXRkU=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\3evekxe22k-356vix0kms.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-07-15T15:42:14.1782562+00:00"}, "54rsR3ixPuy97KTVOPDTDoWQ79IK74wWJHUNgcRM9Ug=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\a471xplc8c-83jwlth58m.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "4+V+8gfVIOo5MYDXGqjwzGxq3roXCy2D5weB4mDi4RM=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\lhihnrvlt0-opubk6fn5b.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Computed", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "BubbleTeaCafe#[.{fingerprint=opubk6fn5b}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\scopedcss\\bundle\\BubbleTeaCafe.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mc315w6khk", "Integrity": "NszthnbP139k9OONJflVzZVoTa+tWRWTrHt3iF7tVXc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\scopedcss\\bundle\\BubbleTeaCafe.styles.css", "FileLength": 497, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "OGNpaJwmtLau2FqteKSZnpOoXoZswbe0TBro+08rJXA=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\fmv4low3a0-opubk6fn5b.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Computed", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "BubbleTeaCafe#[.{fingerprint=opubk6fn5b}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\BubbleTeaCafe.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mc315w6khk", "Integrity": "NszthnbP139k9OONJflVzZVoTa+tWRWTrHt3iF7tVXc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\BubbleTeaCafe.bundle.scp.css", "FileLength": 497, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "om8+GiO1iWLxNR9lPK4jTyzJZqBhclNb/sbyMmqBRMs=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\81kbhefo5v-mrlpezrjn3.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "rNVky69f/u+ZsJSPpFCwP9Sicg6hm8VqYybSSLzXa5c=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\prh8aqr7sf-lzl9nlhx6b.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "F+Qve9ieo2qmDH/6B8CH/B9yyXnzzsDa+DS2xeqHDVw=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\tv340g5b9d-ag7o75518u.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "rjtUgPuQD56LmLNXoi4LAMLsyVcNtnwZu9vJ8D4+LiE=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\ss6a58xtxg-x0q3zqp4vz.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "//ARUJVKDQdWHq3sfXUXueZWCEYFwBeaZjjMPW+BVDQ=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\3ns3qpxgxu-0i3buxo5is.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "ItaFmuMywdIQY0YhO70cdjJpH298j83llhjNhvMYVFo=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\nm3evsuy0d-o1o13a6vjx.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-07-15T15:42:14.1784591+00:00"}, "GS6zkE3TLOSi0lOnYdie44R7WPNQXbK2k/8+d7xuIYc=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\1kr5y3g49k-ttgo8qnofa.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-07-15T15:42:14.2066968+00:00"}, "buXI5ZFYxT1D4jWGWmLvWYVluT629iMikPJ90LAM0ls=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\g03h3o6qhc-2z0ns9nrw6.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-07-15T15:42:14.1926582+00:00"}, "2nL4iY+lWyYJHSM7GwAmC0jiKymh6SaC9eqPeU5WYCI=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\vofk56aydg-muycvpuwrr.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-07-15T15:42:14.1994747+00:00"}, "DBb9UT+svJ2jEzj8TY4acizE0r6sV3tWh0yS0YKqWTQ=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\arcrh64dn3-87fc7y1x7t.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-07-15T15:42:14.2027016+00:00"}, "W64WrNj3wHrWhZvK2Mt5lSyM7uHXgBCO/FxE4OFf6SE=": {"Identity": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\6ib7ji3vp5-mlv21k5csn.gz", "SourceId": "BubbleTeaCafe", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\BubbleTeaCafe\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BubbleTeaCafe", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\BubbleTeaCafe\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-07-15T15:42:14.1782562+00:00"}}, "CachedCopyCandidates": {}}